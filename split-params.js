const fs = require('fs');
const path = require('path');

// 加载参数组合文件
function loadParamsCombinations() {
    try {
        // 数学，物理，地理
        const paramsCombinations = require('./params.json').filter(v => v.studyPhaseCode === '200' && (v.subjectName === '数学' || v.subjectName === '物理' || v.subjectName === '地理'));
        console.log(`✅ 已加载参数组合文件: ${paramsCombinations.length} 个组合`);
        return paramsCombinations;
    } catch (error) {
        console.error('❌ 未找到参数组合文件 params.json:', error.message);
        return [];
    }
}

// 将数组均分成 n 份
function splitArrayIntoChunks(array, numberOfChunks) {
    if (numberOfChunks <= 0) {
        throw new Error('分割份数必须大于0');
    }
    
    if (numberOfChunks >= array.length) {
        // 如果分割份数大于等于数组长度，每个元素一份
        return array.map(item => [item]);
    }
    
    const chunks = [];
    const itemsPerChunk = Math.floor(array.length / numberOfChunks);
    const remainder = array.length % numberOfChunks;
    
    let startIndex = 0;
    
    for (let i = 0; i < numberOfChunks; i++) {
        // 前 remainder 个分组多分配一个元素
        const currentChunkSize = itemsPerChunk + (i < remainder ? 1 : 0);
        const endIndex = startIndex + currentChunkSize;
        
        chunks.push(array.slice(startIndex, endIndex));
        startIndex = endIndex;
    }
    
    return chunks;
}

// 保存分割后的参数文件
function saveChunks(chunks, baseFilename = 'params') {
    const savedFiles = [];
    
    chunks.forEach((chunk, index) => {
        const filename = `${baseFilename}_${index + 1}.json`;
        const filepath = path.join('./', filename);
        
        try {
            fs.writeFileSync(filepath, JSON.stringify(chunk, null, 2), 'utf8');
            savedFiles.push({
                filename: filename,
                count: chunk.length,
                path: filepath
            });
            console.log(`💾 已保存 ${chunk.length} 个参数组合到: ${filename}`);
        } catch (error) {
            console.error(`❌ 保存文件 ${filename} 失败:`, error.message);
        }
    });
    
    return savedFiles;
}

// 显示分割统计信息
function showSplitStatistics(originalCount, chunks, savedFiles) {
    console.log('\n📊 分割统计信息:');
    console.log(`   原始参数组合数: ${originalCount}`);
    console.log(`   分割份数: ${chunks.length}`);
    console.log(`   总分配数: ${chunks.reduce((sum, chunk) => sum + chunk.length, 0)}`);
    
    console.log('\n📋 各文件详情:');
    console.log('文件名'.padEnd(20) + '参数数量'.padEnd(12) + '占比');
    console.log('-'.repeat(50));
    
    savedFiles.forEach((file, index) => {
        const percentage = ((file.count / originalCount) * 100).toFixed(1);
        console.log(`${file.filename.padEnd(20)} ${file.count.toString().padEnd(12)} ${percentage}%`);
    });
    
    // 检查是否均匀分布
    const maxCount = Math.max(...savedFiles.map(f => f.count));
    const minCount = Math.min(...savedFiles.map(f => f.count));
    const difference = maxCount - minCount;
    
    console.log('\n📈 分布均匀性:');
    if (difference <= 1) {
        console.log('   ✅ 分布非常均匀（最大差异: ' + difference + '）');
    } else {
        console.log('   ⚠️ 分布不够均匀（最大差异: ' + difference + '）');
    }
}

// 生成分割报告
function generateSplitReport(originalCount, chunks, savedFiles, numberOfChunks) {
    const report = {
        splitTime: new Date().toISOString(),
        original: {
            filename: 'params.json',
            count: originalCount
        },
        splitConfig: {
            numberOfChunks: numberOfChunks,
            actualChunks: chunks.length
        },
        files: savedFiles.map((file, index) => ({
            filename: file.filename,
            count: file.count,
            percentage: ((file.count / originalCount) * 100).toFixed(1) + '%',
            startIndex: chunks.slice(0, index).reduce((sum, chunk) => sum + chunk.length, 0),
            endIndex: chunks.slice(0, index + 1).reduce((sum, chunk) => sum + chunk.length, 0) - 1
        })),
        statistics: {
            totalDistributed: chunks.reduce((sum, chunk) => sum + chunk.length, 0),
            maxPerFile: Math.max(...savedFiles.map(f => f.count)),
            minPerFile: Math.min(...savedFiles.map(f => f.count)),
            difference: Math.max(...savedFiles.map(f => f.count)) - Math.min(...savedFiles.map(f => f.count))
        }
    };
    
    const reportFile = './params-split-report.json';
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
    console.log(`\n📄 已生成分割报告: ${reportFile}`);
    
    return report;
}

// 清理之前的分割文件
function cleanupPreviousSplitFiles(baseFilename = 'params') {
    let cleanedCount = 0;
    
    // 查找现有的分割文件（最多检查100个）
    for (let i = 1; i <= 100; i++) {
        const filename = `${baseFilename}_${i}.json`;
        if (fs.existsSync(filename)) {
            try {
                fs.unlinkSync(filename);
                cleanedCount++;
            } catch (error) {
                console.warn(`⚠️ 删除文件 ${filename} 失败: ${error.message}`);
            }
        }
    }
    
    if (cleanedCount > 0) {
        console.log(`🗑️ 已清理 ${cleanedCount} 个之前的分割文件`);
    }
    
    return cleanedCount;
}

// 主函数
function main() {
    // 解析命令行参数
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.error('❌ 请指定分割份数');
        console.log('使用方法: node split-params.js <分割份数> [基础文件名]');
        console.log('示例: node split-params.js 5');
        console.log('示例: node split-params.js 3 params');
        return;
    }
    
    const numberOfChunks = parseInt(args[0]);
    const baseFilename = args[1] || 'params';
    
    if (isNaN(numberOfChunks) || numberOfChunks <= 0) {
        console.error('❌ 分割份数必须是大于0的整数');
        return;
    }
    
    console.log(`🔪 开始将参数组合分割成 ${numberOfChunks} 份...\n`);
    
    // 加载参数组合
    const paramsCombinations = loadParamsCombinations();
    
    if (paramsCombinations.length === 0) {
        console.error('❌ 没有找到参数组合，退出程序');
        return;
    }
    
    // 检查分割份数是否合理
    if (numberOfChunks > paramsCombinations.length) {
        console.warn(`⚠️ 分割份数(${numberOfChunks})大于参数组合数(${paramsCombinations.length})，将创建 ${paramsCombinations.length} 个文件，每个文件包含1个参数组合`);
    }
    
    try {
        // 清理之前的分割文件
        cleanupPreviousSplitFiles(baseFilename);
        
        // 分割参数组合
        const chunks = splitArrayIntoChunks(paramsCombinations, numberOfChunks);
        
        // 保存分割后的文件
        const savedFiles = saveChunks(chunks, baseFilename);
        
        // 显示统计信息
        showSplitStatistics(paramsCombinations.length, chunks, savedFiles);
        
        // 生成报告
        generateSplitReport(paramsCombinations.length, chunks, savedFiles, numberOfChunks);
        
        console.log('\n✅ 参数组合分割完成！');
        
        console.log('\n💡 使用提示:');
        console.log('   - 现在可以在不同的进程或机器上并行处理这些文件');
        console.log('   - 将对应的文件重命名为 params.json 后使用 PlaywrightCrawler');
        console.log('   - 查看 params-split-report.json 获取详细的分割信息');
        
    } catch (error) {
        console.error('❌ 分割过程中发生错误:', error.message);
    }
}

// 验证分割结果的函数
function verifySplit(numberOfChunks, baseFilename = 'params') {
    console.log('\n🔍 验证分割结果...');
    
    const originalParams = loadParamsCombinations();
    let totalVerified = 0;
    let allFilesValid = true;
    
    for (let i = 1; i <= numberOfChunks; i++) {
        const filename = `${baseFilename}_${i}.json`;
        
        try {
            if (!fs.existsSync(filename)) {
                console.error(`❌ 文件 ${filename} 不存在`);
                allFilesValid = false;
                continue;
            }
            
            const fileContent = fs.readFileSync(filename, 'utf8');
            const params = JSON.parse(fileContent);
            
            if (!Array.isArray(params)) {
                console.error(`❌ 文件 ${filename} 格式错误：不是数组`);
                allFilesValid = false;
                continue;
            }
            
            totalVerified += params.length;
            console.log(`✅ ${filename}: ${params.length} 个参数组合`);
            
        } catch (error) {
            console.error(`❌ 验证文件 ${filename} 失败: ${error.message}`);
            allFilesValid = false;
        }
    }
    
    if (allFilesValid && totalVerified === originalParams.length) {
        console.log(`✅ 验证通过：分割后总数 ${totalVerified} = 原始总数 ${originalParams.length}`);
    } else {
        console.error(`❌ 验证失败：分割后总数 ${totalVerified} ≠ 原始总数 ${originalParams.length}`);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    loadParamsCombinations,
    splitArrayIntoChunks,
    saveChunks,
    showSplitStatistics,
    generateSplitReport,
    cleanupPreviousSplitFiles,
    verifySplit
}; 