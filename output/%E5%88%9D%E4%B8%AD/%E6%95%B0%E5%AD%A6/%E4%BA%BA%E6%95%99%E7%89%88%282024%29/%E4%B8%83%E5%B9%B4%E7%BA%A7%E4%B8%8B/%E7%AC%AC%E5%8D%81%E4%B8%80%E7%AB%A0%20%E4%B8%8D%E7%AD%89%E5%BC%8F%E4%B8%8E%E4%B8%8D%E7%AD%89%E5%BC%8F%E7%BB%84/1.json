{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 0, "pageSize": 10, "totalPage": 1856, "total": "18553", "list": [{"questionId": "593317519181520896", "questionArticle": "<p>1．南宁是中国著名的水果产地，盛产香蕉和芒果．某水果商准备收购一批香蕉和芒果，运往外地销售．已知 $ 2 $ 吨香蕉和 $ 3 $ 吨芒果的收购成本为 $ 2.8 $ 万元； $ 4 $ 吨香蕉和 $ 1 $ 吨芒果的收购成本为 $ 2.6 $ 万元．</p><p>（1）每吨香蕉和每吨芒果的收购成本各是多少万元？</p><p>（2）该水果商计划租用货车运输水果，货车公司规定：若运输总重量不超过 $ 6 $ 吨，每吨运费 $ 120 $ 元；若超过 $ 6 $ 吨，超过部分每吨运费 $ 180 $ 元．水果商希望运费不超过 $ 990 $ 元，那么他最多能收购并运输多少吨水果？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西南宁二中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593317488915423232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "593317488915423232", "title": "2025年广西南宁市第二中学中考三模数学测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871349120442368", "questionArticle": "<p>2．为了拓宽学生视野，某校计划组织900名师生开展以“追寻红色足迹，传承红色精神”为主题的研学活动．某旅游公司有 $ A，B $ 两种型号的客车可以租用，已知1辆 $ \\mathrm{ A } $ 型车和1辆 $ B $ 型车可以载乘客85人，3辆 $ \\mathrm{ A } $ 型车和2辆 $ B $ 型车可以载乘客210人．</p><p>（1）一辆 $ \\mathrm{ A } $ 型客车和一辆 $ B $ 型客车分别可以载乘客多少人？</p><p>（2）若租用 $ \\mathrm{ A } $ 型客车和 $ B $ 型客车（两种都租）刚好能装载这900名师生，请求出所有的租车方案？</p><p>（3）该校计划租用 $ A，B $ 两种型号的客车共22辆，其中 $ \\mathrm{ A } $ 型客车数量的一半不少于 $ B $ 型客车的数量，共有多少种租车方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16434|16438|16490", "keyPointNames": "方案问题|和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871338387218432", "questionArticle": "<p>3．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax+3y=12 \\\\ x-3y=0 \\end{cases}  $ 的解都为整数，且关于<i>x</i>的不等式组 $ \\begin{cases} 2\\left ( { x+1 } \\right )   &lt;  x+5 \\\\ 4x &gt; a-5 \\end{cases}  $ ，恰有3个整数解，则所有满足条件的整数<i>a</i>的和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．10B．8C．6D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592871345228128256", "questionArticle": "<p>4．已知 $ x+y+z=15，-3x-y+z=-25 $ ．若 $ x{ { &nbsp; } }、{ { &nbsp; } }y{ { &nbsp; } }、{ { &nbsp; } }z $ 皆为非负数；若 $ N=x+4y+2z $ ，则 $ N $ 的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16489|16535", "keyPointNames": "解一元一次不等式组|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871340530507776", "questionArticle": "<p>5．如图，数轴上所表示的关于<i>x</i>的不等式的解集为：<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592870982798323727/images/img_16.png\" style=\"vertical-align:middle;\" width=\"199\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "28266", "keyPointNames": "在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871742890094592", "questionArticle": "<p>6．有 $ \\mathrm{ A } $ 、 $ B $ 两种型号呼吸机，若购买 $ 6 $ 台 $ \\mathrm{ A } $ 型呼吸机和 $ 2 $ 台 $ B $ 型呼吸机共需 $ 12 $ 万元．若购买 $ 3 $ 台 $ \\mathrm{ A } $ 型呼吸机和 $ 5 $ 台 $ B $ 型呼吸机共需 $ 10.8 $ 万元．</p><p>（1）求 $ \\mathrm{ A } $ 、 $ B $ 两种型号呼吸机每台分别多少万元？</p><p>（2）采购员想采购 $ \\mathrm{ A } $ 、 $ B $ 两种型号呼吸机共 $ 30 $ 台，预计总费用低于 $ 40 $ 万元，请问 $ \\mathrm{ A } $ 型号呼吸机最多购买几台？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西光中学 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871718114340864", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871718114340864", "title": "陕西省西安市新城区西光中学教育集团多校协作2024−2025学年八年级下学期6月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871736464420864", "questionArticle": "<p>7．解不等式组 $ \\begin{cases} x+1  &lt;  2x-1 \\\\ \\dfrac { 2x-5 } { 3 }\\leqslant  1 \\end{cases}  $ ，将解集表示在所给的数轴上，并求出整数解．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592871638003130368/images/img_10.png\" style=\"vertical-align:middle;\" width=\"359\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西光中学 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16489|28266", "keyPointNames": "解一元一次不等式组|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871718114340864", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871718114340864", "title": "陕西省西安市新城区西光中学教育集团多校协作2024−2025学年八年级下学期6月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871731913601024", "questionArticle": "<p>8．关于<i>x</i>的不等式组 $ \\begin{cases} x &gt; m+3 \\\\ 5x-2  &lt;  4x+1 \\end{cases}  $ 的整数解仅有4个，则<i>m</i>的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -5\\leqslant  m  &lt;  -4 $ B． $ -5  &lt;  m\\leqslant  -4 $ C． $ -4\\leqslant  m  &lt;  -3 $ D． $ -4  &lt;  m\\leqslant  -3 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西光中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16489", "keyPointNames": "解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871718114340864", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871718114340864", "title": "陕西省西安市新城区西光中学教育集团多校协作2024−2025学年八年级下学期6月期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592871726951739392", "questionArticle": "<p>9．若 $ m &gt; n $ ，则下列不等式正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ m-1  &lt;  n-1 $ B． $ \\dfrac { m } { 4 } &gt; \\dfrac { n } { 4 } $ C． $ 5m  &lt;  5n $ D． $ -7m\\mathrm{  &gt;  }-7n $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西光中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16482", "keyPointNames": "不等式的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871718114340864", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871718114340864", "title": "陕西省西安市新城区西光中学教育集团多校协作2024−2025学年八年级下学期6月期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592872120171929600", "questionArticle": "<p>10．求不等式 $ \\dfrac { x-2 } { 4 }\\geqslant  \\dfrac { 2x } { 3 }-1 $ 的最大整数解．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16485", "keyPointNames": "解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872096847405056", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872096847405056", "title": "陕西省西安市高新第二初级中学2024−2024学年九年级下学期5月中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 1, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146525"], "categoryId": ""}}, "crawlInfo": {"pageNum": 1, "timestamp": "2025-07-01T02:30:38.267Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十一章 不等式与不等式组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E4%B8%80%E7%AB%A0%20%E4%B8%8D%E7%AD%89%E5%BC%8F%E4%B8%8E%E4%B8%8D%E7%AD%89%E5%BC%8F%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十一章 不等式与不等式组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十一章 不等式与不等式组", "encoded": "%E7%AC%AC%E5%8D%81%E4%B8%80%E7%AB%A0%20%E4%B8%8D%E7%AD%89%E5%BC%8F%E4%B8%8E%E4%B8%8D%E7%AD%89%E5%BC%8F%E7%BB%84"}]}}}