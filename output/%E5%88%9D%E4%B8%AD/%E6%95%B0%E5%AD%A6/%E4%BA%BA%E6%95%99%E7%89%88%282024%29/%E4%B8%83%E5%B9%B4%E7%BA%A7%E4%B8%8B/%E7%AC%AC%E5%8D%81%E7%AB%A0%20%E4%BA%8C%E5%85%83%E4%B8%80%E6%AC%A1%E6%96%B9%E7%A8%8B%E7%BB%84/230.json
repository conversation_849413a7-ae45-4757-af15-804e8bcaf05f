{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 229, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "526894201939730432", "questionArticle": "<p>1．已知 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} a+2b=4 \\\\ 2a+b=5 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ 是关于<i>a</i>，<i>b</i>的二元一次方程组，则 $ a+b $ 是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1　　　　B．3　　　　C．9　　　　D．12</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527898734094491648", "questionArticle": "<p>2．《九章算术》是人类科学史上应用数学的“算经之首”，其书中卷八方程[七]中记载：“今有牛五、羊二，直金十两．牛二、羊五，直金八两．牛、羊各直金几何？”题目大意是：“5头牛、2只羊共值金10两．2头牛、5只羊共值金8两，每头牛、每只羊各值金多少两？”设每头牛值金 $ x $ 两，每只羊值金 $ y $ 两，那么下面列出的方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $ B． $ \\begin{cases} x+y=8 \\\\ 2x+xy=10 \\end{cases}  $ C． $ \\begin{cases} 5x+2y=8 \\\\ x+y=10 \\end{cases}  $ D． $ \\begin{cases} 5x+2y=8 \\\\ 2x+5y=10 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东胜利一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-02", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527898729606586368", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527898729606586368", "title": "山东省东营市胜利第一初级中学2024−2025学年九年级12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526893841892286464", "questionArticle": "<p>3．对于代数式 $ m $ ， $ n $ ，定义运算“ $ \\otimes  $ ”： $ m\\otimes n=\\dfrac { m+n-6 } { mn } $ ，例如： $ 4\\otimes 2=\\dfrac { 4+2-6 } { 4\\times 2 } $ ，若 $ \\left ( { x-1 } \\right ) \\otimes \\left ( { x+2 } \\right ) =\\dfrac { A } { x-1 }+\\dfrac { B } { x+2 } $ ，则 $ A+2B= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东潍坊 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-02", "keyPointIds": "16369|16424", "keyPointNames": "分式的加减|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526893835219148800", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "526893835219148800", "title": "山东省潍坊市高密市二校联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "526894203923636224", "questionArticle": "<p>4．如图是由7个形状、大小都相同的小长方形和阴影部分无缝隙拼合而成的一个大长方形，则图中阴影部分的面积为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/04/2/20/0/0/0/530743065813753857/images/img_1.png\" style='vertical-align:middle;' width=\"99\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-02", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "463459185600012288", "title": "江西省南昌市外国语教育集团2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "526894202933780480", "questionArticle": "<p>5．我国古代数学名著《孙子算经》中记载：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是，用一根绳子去量一根木条，绳子剩余4.5尺；将绳子对折再量木条，木条剩余1尺．问木条长多少尺？如果设木条长<i>x</i>尺，绳子长<i>y</i>尺，那么可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} y=x+4.5 \\\\ \\dfrac { 1 } { 2 }y=x-1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p><p>B． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} y=x+4.5 \\\\ y=2x-1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p><p>C． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} y=x-4.5 \\\\ \\dfrac { 1 } { 2 }y=x+1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p><p>D． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} y=x-4.5 \\\\ y=2x+1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-02", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "220492348865683456", "title": "河南省濮阳市油田联考2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527898842034905088", "questionArticle": "<p>6．综合与实践：如何称量一个空矿泉水瓶的重量？</p><p>器材：如图1所示的一架自制天平，支点 $ O $ 固定不变，左侧托盘固定在点 $ A $ 处，右侧托盘的点 $ P $ 可以在横梁 $ BC $ 段滑动．已知 $ OA=OC=12{ \\rm{ c } }{ \\rm{ m } } $ ， $ BC=28{ \\rm{ c } }{ \\rm{ m } } $ ，一个 $ 100{ \\rm{ g } } $ 的砝码．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/04/2/20/0/0/0/530739795124854785/images/img_1.png\" style='vertical-align:middle;' width=\"490\" alt=\"试题资源网 https://stzy.com\"></p><p>链接：根据杠杆原理，平衡时：左盘物体重量 $ \\times OA= $ 右盘物体重量 $ \\times OP $ （不计托盘与横梁重量）．</p><p>(1)左侧托盘放置砝码，右侧托盘放置物体，设右侧托盘放置物体的重量为 $ y({ \\rm{ g } }) $ ， $ OP $ 长 $ x({ \\rm{ c } }{ \\rm{ m } }) $ ．当天平平衡时，求 $ y $ 关于 $ x $ 的函数表达式，并求 $ y $ 的取值范围；</p><p>(2)由于一个空的矿泉水瓶太轻无法称量，小组进行如下操作：左侧托盘放置砝码，右侧托盘放置矿泉水瓶，如图2．滑动点 $ P $ 至点 $ B $ ，空瓶中加入适量的水使天平平衡，再向瓶中加入等量的水，发现点 $ P $ 移动到 $ PC $ 长为 $ 12{ \\rm{ c } }{ \\rm{ m } } $ 时，天平平衡．求这个空矿泉水瓶的重量．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|110000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京景山学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 4, "createTime": "2025-01-02", "keyPointIds": "16441|16579", "keyPointNames": "其他问题|反比例函数的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243329943642112", "questionFeatureName": "综合与实践题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "580243329943642112", "title": "北京景山学校2024~2025学年下学期期中考试 八年级数学试题", "paperCategory": 1}, {"id": "527898833105231872", "title": "山东省临沂市五校2024−2025学年九年级上学期12月自测数学试卷", "paperCategory": 1}, {"id": "449188568906375168", "title": "2024年湖南省长沙市青竹湖湘一外国语学校中考二模数学试题", "paperCategory": 1}, {"id": "447523644400508928", "title": "2024年湖南省长沙市湘江新区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526893119171764224", "questionArticle": "<p>7．（1）计算： $ \\sqrt { 12 }-\\sqrt { 6 }\\div {\\left( { \\sqrt { 2 } } \\right) ^ {-1}}-\\left ( { \\sqrt { 3 }+1 } \\right ) \\left ( { \\sqrt { 3 }-1 } \\right )  $ ；</p><p>（2）解方程组 $ \\begin{cases} x+2y=0 \\\\ 3x+4y=6 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-01", "keyPointIds": "16372|16379|16424", "keyPointNames": "负整数指数幂|二次根式的性质和化简|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526893111806566400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526893111806566400", "title": "辽宁省沈阳市南昌中学2024−2025学年八年级上学期12月数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527899274450870272", "questionArticle": "<p>8．甲、乙两个两位数，若把甲数放在乙数的左边，组成的四位数是乙数的201倍；若把乙数放在甲数的左边，组成的四位数比上面的四位数小1188，求这两个数．如果设甲数为<i>x</i>，乙数为<i>y</i>，则得到的二元一次方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 100x+y=100x+y+1188 \\\\ 100y+x=201x \\end{cases}  $　　　　B． $ \\begin{cases} 100x+y=201x \\\\ 100y+x=100x+y-1188 \\end{cases}  $</p><p>C． $ \\begin{cases} 100x+y=100x+y-1188 \\\\ 100y+x=201y \\end{cases}  $　　　　D． $ \\begin{cases} 100x+y=201y \\\\ 100y+x=100x+y-1188 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西山西大学附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-01", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899268750811136", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899268750811136", "title": "山西省太原市小店区山西大学附属中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527899275725938688", "questionArticle": "<p>9．小华在解方程组 $ \\begin{cases} 2x+y=6① \\\\ x+2y=-3② \\end{cases}  $ 时，具体解法如下：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 280.7pt;\"><p>解：①×2得， $ 4x+2y=12 $ ③，…………………（第一步）</p><p>③－②得， $ 3x=9 $ ，……………………（第二步）</p><p>所以， $ x=3 $ ，</p><p>将 $ x=3 $ 代入①得， $ y=0 $ ．………………（第三步）</p><p>所以这个方程组的解是 $ \\begin{cases} x=3 \\\\ y=0 \\end{cases}  $ ． </p></td></tr></table><p>任务：</p><p>(1)这种求解二元一次方程组的解法叫做<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（填“代入消元法”或“加减消元法”），以上求解步骤中，第一步的依据是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)以上解答过程从第<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>步开始出现错误，具体错误是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(3)请直接写出该二元一次方程组的正确解<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西山西大学附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-01", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899268750811136", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899268750811136", "title": "山西省太原市小店区山西大学附属中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527899275918876672", "questionArticle": "<p>10．某学校科技节展示了使用无人配送车和无人机配送货物．已知一台无人机一次可运送4千克货物，一台无人配送车一趟可运送80千克货物．活动提供了无人机和无人配送车共20台一趟共运送460千克货物，那么运送货物使用的无人机和无人配送车各有几台？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西山西大学附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-01", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899268750811136", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899268750811136", "title": "山西省太原市小店区山西大学附属中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 230, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 230, "timestamp": "2025-07-01T02:27:55.874Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}