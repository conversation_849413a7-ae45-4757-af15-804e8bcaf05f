{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 220, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "534115612265586688", "questionArticle": "<p>1．在长为18m，宽为15m的长方形空地上，沿平行于长方形各边的方向分别割出三个大小完全一样的小长方形花圃，其示意图如图所示，则其中一个小长方形花圃的面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/13/2/1/0/0/0/534115571333373957/images/img_6.png\" style=\"vertical-align:middle;\" width=\"174\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;&nbsp;&nbsp;</p><p>A． $ 10{ \\rm{ m } }{^{{ { 2 } }}} $ B． $ 12{ \\rm{ m } }{^{{ { 2 } }}} $ C． $ 18{ \\rm{ m } }{^{{ { 2 } }}} $ D． $ 28{ \\rm{ m } }{^{{ { 2 } }}} $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济外 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534115606116737024", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534115606116737024", "title": "山东省济南市历城区济南外国语学校2024−2025学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "534114415601295360", "questionArticle": "<p>2．酸辣粉是重庆的特色美食，三峡广场某小吃店推出两款酸辣粉，一款是“经典手工酸辣粉”，另一款是“肉沫哨子酸辣粉”．已知1份“经典手工酸辣粉”和2份“肉沫哨子酸辣粉”需34元；3份“经典手工酸辣粉”和1份“肉沫哨子酸辣粉”需42元．</p><p>(1)求“经典手工酸辣粉”和“肉沫哨子酸辣粉”的单价；</p><p>(2)红薯粉条是制作酸辣粉的原材料之一，该小吃店老板发现今年第三季度平均每千克红薯粉条的价格比第二季度上涨了 $ 20\\% $ ，第三季度花600元买到的红薯粉条数量比第二季度花同样的钱买到的红薯粉条数量少了10千克，求第三季度红薯粉条的单价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆渝北 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-14", "keyPointIds": "16424|16438|16471|16476", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|解分式方程|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534114404826128384", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534114404826128384", "title": "重庆市渝北区六校联盟2024−2025学年九年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "392452290584551424", "title": "重庆市沙坪坝区第七中学校2023-2024学年九年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532313527475806208", "questionArticle": "<p>3．上游<i>A</i>地与下游<i>B</i>地相距 $ 80{ \\rm{ k } }{ \\rm{ m } } $ ，一艘游船计划先从<i>A</i>地出发顺水航行到达<i>B</i>地，然后立即返回<i>A</i>地．已知航行过程中，水流速度和该船的静水速度都不变．如图是这艘游船离<i>A</i>地的距离 $ y（{ \\rm{ k } }{ \\rm{ m } }） $ 与航行时间 $ x（{ \\rm{ h } }） $ 之间的关系图象．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/08/2/1/0/0/0/532313445942730778/images/img_27.png\" style=\"vertical-align:middle;\" width=\"191\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求<i>y</i>与<i>x</i>之间的函数表达式；</p><p>(2)若一艘货船在<i>A</i>地下游 $ 24{ \\rm{ k } }{ \\rm{ m } } $ 处，货船与<i>A</i>地的游船同时前往<i>B</i>地，已知货船的静水速度为 $ 6{ \\rm{ k } }{ \\rm{ m } }{ \\rm{ / } }{ \\rm{ h } } $ ，求游船在前往<i>B</i>地的航行途中与货船相遇的时间．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16430|26247", "keyPointNames": "行程问题|待定系数法求一次函数解析式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532313518210588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532313518210588672", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（一）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532313526376898560", "questionArticle": "<p>4．（1）计算 $ \\sqrt { 12 }-{\\left( { π-3.14 } \\right) ^ {0}}+\\left  | { 2-\\sqrt { 3 } } \\right  | +{\\left( { \\dfrac { 1 } { 2 } } \\right) ^ {-2}} $ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p><p>（2）解方程组： $ \\begin{cases} \\dfrac { x } { 3 }+1=y \\\\ 2\\left ( { x+1 } \\right ) -y=6 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16323|16372|16423", "keyPointNames": "零指数幂|负整数指数幂|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532313518210588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532313518210588672", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（一）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532313523226976256", "questionArticle": "<p>5．甲、乙两根绳共长17米，如果甲绳减去它的 $ \\dfrac { 1 } { 5 } $ ，乙绳增加1米，那么两根绳长相等，若设甲绳长<i>x</i>米，乙绳长<i>y</i>米，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=17 \\\\ x-\\dfrac { 1 } { 5 }x=y+1 \\end{cases}  $ B． $ \\begin{cases} x+y=17 \\\\ x+\\dfrac { 1 } { 5 }x=y-1 \\end{cases}  $ C． $ \\begin{cases} x+y=17 \\\\ x-\\dfrac { 1 } { 5 }=y+1 \\end{cases}  $ D． $ \\begin{cases} x+y=17 \\\\ x-\\dfrac { 1 } { 5 }x=y-1 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532313518210588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532313518210588672", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（一）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "532313906670247936", "questionArticle": "<p>6．因粤港澳大湾区和中国特色社会主义先行示范区的双重利好，深圳已成为国内外游客最喜欢的旅游目的地城市之一，东部华侨城景区成为深圳著名旅游“网红打卡地”．已知在2024年“十一”长假期间，东部华侨城景区共接待游客达20万人次，其中该景区的成人票每张200元，学生票按成人票五折优惠．某班在该景区内组织活动，教师和学生一共去了30人，门票共需3300元．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 201.35pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/08/2/1/0/0/0/532313867751301125/images/img_17.png\" style=\"vertical-align:middle;\" width=\"31\" alt=\"试题资源网 https://stzy.com\">根据销售经验，在旅游旺季，若每杯定价25元，则平均每天可销售300杯，若每杯价格降低1元，则平均每天可多销售30杯．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 226pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/08/2/1/0/0/0/532313867751301126/images/img_18.png\" style=\"vertical-align:middle;\" width=\"35\" alt=\"试题资源网 https://stzy.com\">已知每杯奶茶成本价为5元，假设在2025年“十一”期间每杯奶茶降价<i>m</i>元时，店家此款奶茶能实现平均每天6720元的利润额，请求出<i>m</i>的值．</p></td></tr></table><p>(1)参与活动的教师和学生各有多少人？</p><p>(2)在该景区内有一家奶茶店销售的一款奶茶备受游客喜爱，店家决定在2024年“十一”期间进行降价促销活动，既能让顾客获得最大优惠，又可让店家实现相应的利润额．请依据以上对话，完成本题．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市江津实验中学等校 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16438|16463", "keyPointNames": "和差倍分问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532313896276762624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532313896276762624", "title": "重庆市江津区实验中学、李市中学、白沙中学等 五校2024−2025学年上学期期末联考九年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "533729710771052544", "questionArticle": "<p>7．甲、乙两车匀速从 $ \\mathrm{ A } $ 地到 $ B $ 地，甲出发半小时后，乙车以每小时100千米的速度沿同一路线行驶，两车分别到达目的地后停止，甲、乙两车之间的距离 $ y $ （千米）与甲车行驶的时间 $ x $ （小时）之间的关系如图所示，则下列说法错误的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/12/2/1/0/0/0/533729686876102660/images/img_7.png\" style=\"vertical-align:middle;\" width=\"260\" alt=\"试题资源网 https://stzy.com\"></p><p>A．甲车的行驶速度为 $ 80{ \\rm{ k } }{ \\rm{ m } }{ \\rm{ / } }{ \\rm{ h } } $ </p><p>B．当乙车行驶2小时，乙车追上甲车</p><p>C．当甲车行驶6小时，甲、乙两车相距 $ 70{ \\rm{ k } }{ \\rm{ m } } $ </p><p>D． $ \\mathrm{ A } $ 、 $ B $ 两地的距离为 $ 700{ \\rm{ k } }{ \\rm{ m } } $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市长寿中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16430|16519", "keyPointNames": "行程问题|从函数的图象获取信息", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "533729701627469824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "533729701627469824", "title": "重庆市长寿中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "533729716844404736", "questionArticle": "<p>8．（1）计算： $ \\left ( { \\sqrt { 12 }+\\sqrt { 3 } } \\right ) \\times \\sqrt { 6 }-2\\sqrt { \\dfrac { 1 } { 2 } } $ ；</p><p>（2）解方程组： $ \\begin{cases} 3x+y=2 \\\\ 5x-2\\left ( { y-1 } \\right ) =9 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市长寿中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16389|16424", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "533729701627469824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "533729701627469824", "title": "重庆市长寿中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534116216102756352", "questionArticle": "<p>9．某货运电梯限重标志显示，载重总质量禁止超过 $ 2000{ \\rm{ k } }{ \\rm{ g } } $ ．现要用此货运电梯装运一批设备，每套设备由<point-tag>1个甲部件和2个乙部件</point-tag>组成．已知2个甲部件和1个乙部件总质量为 $ 140{ \\rm{ k } }{ \\rm{ g } } {\\rm ，3} $ 个甲部件和2个乙部件质量相同．</p><p>(1)求1个甲部件和1个乙部件的质量各是多少 $ { \\rm{ k } }{ \\rm{ g } } $ ？</p><p>(2)每次装运都需要两名工人装卸，设备需要成套装运，现已知两名装卸工人质量分别为 $ 72{ \\rm{ k } }{ \\rm{ g } } $ 和 $ 78{ \\rm{ k } }{ \\rm{ g } } $ ，则货运电梯一次最多可装运多少套设备？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江嘉兴 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16438|16485|16486", "keyPointNames": "和差倍分问题|解一元一次不等式|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534116207768674304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534116207768674304", "title": "浙江省嘉兴一中实验学校2024−2025学年上学期12月月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534116215880458240", "questionArticle": "<p>10．已知方程组 $ \\begin{cases} x+y=6-m \\\\ x-y=-2+3m \\end{cases}  $ 的解满足 $ x $ 为正数， $ y $ 为非负数．</p><p>(1)求 $ m $ 的取值范围；</p><p>(2)若不等式 $ \\left ( { 2m-1 } \\right ) x-2m  &lt;  -1 $ 的解为 $ x &gt; 1 $ ．求 $ m $ 的整数值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江嘉兴 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-14", "keyPointIds": "16424|16482|16489", "keyPointNames": "加减消元法解二元一次方程组|不等式的性质|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534116207768674304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534116207768674304", "title": "浙江省嘉兴一中实验学校2024−2025学年上学期12月月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 221, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 221, "timestamp": "2025-07-01T02:26:49.786Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}