{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 232, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "527583386220666880", "questionArticle": "<p>1．已知方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array} {l} 3x-\\left ( { m-3 } \\right ) y{^{\\left  | { m-2 } \\right  | -2}}=1 \\\\ \\left ( { m+1 } \\right ) x=-2 \\end{array} \\hspace{-0.5em} \\right.  $ 是关于 $ x $ ， $ y $ 的二元一次方程组，则 $ m $ 的值<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西吉安 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-29", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527583381254610944", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527583381254610944", "title": "江西省吉安市2024−2025学年八年级上学期12月考数学试题", "paperCategory": 1}, {"id": "527897134026235904", "title": "江西省吉安市八校联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "526524257930616832", "questionArticle": "<p>2．计算、解方程：</p><p>(1) $ \\sqrt { \\dfrac { 4 } { 3 } }-{\\left( { 2021-π } \\right) ^ {0}}+\\left  | { \\sqrt { 3 }-2 } \\right  |  $ ；</p><p>(2) $ -{\\left( { \\sqrt { 3 }+1 } \\right) ^ {2}}-\\left ( { \\sqrt { 5 }+3 } \\right ) \\times \\left ( { \\sqrt { 5 }-3 } \\right )  $ ；</p><p>(3) $ \\begin{cases} 2x+5y=-1 \\\\ 3x-7y=13 \\end{cases}  $ ；</p><p>(4) $ \\begin{cases} 3x-y=9 \\\\ \\dfrac { x } { 2 }-\\dfrac { y } { 3 }=2 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安市第三中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-28", "keyPointIds": "16323|16379|16424", "keyPointNames": "零指数幂|二次根式的性质和化简|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526524250452172800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526524250452172800", "title": "陕西省西安市第三中学名校2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526524796412141568", "questionArticle": "<p>3．解方程组：</p><p>(1) $ \\begin{cases} 2x+4y=5 \\\\ x=1-y \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 4x+y=5 \\\\ \\dfrac { x-1 } { 2 }+\\dfrac { y } { 3 }=2 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024陕西陕西师范大学附属中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 1, "createTime": "2024-12-28", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526524788237443072", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526524788237443072", "title": "陕西省西安市雁塔区陕西师范大学附属中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526524795686526976", "questionArticle": "<p>4．古典数学文献《增删算法统宗·六均输》中有这样一道题：甲、乙两人一同放牧，两人暗地里在数羊的数量．如果乙给甲9只羊，则甲的羊数量为乙的两倍；如果甲给乙9只羊，则两人的羊数量相同．则乙的羊数量为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>只．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西陕西师范大学附属中学 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526524788237443072", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526524788237443072", "title": "陕西省西安市雁塔区陕西师范大学附属中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "526524623644565504", "questionArticle": "<p>5．自然灾害的突发性,灾区急需帐篷&nbsp;&nbsp;.某企业急灾区所急,准备捐助甲﹑乙两种型号的帐篷共2000顶，其中甲种帐篷每顶安置6人，乙种帐篷每顶安置4人，共安置9000人，求甲乙各捐助多少顶帐篷？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西光中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526524615251763200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "526524615251763200", "title": "陕西省西安市新城区西光中学教育集团联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526524794172383232", "questionArticle": "<p>6．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} ax+by=6 \\\\ bx+ay=8 \\end{cases}  $ 的解为 $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ ，若 $ m $ ， $ n $ 满足二元一次方程组 $ \\begin{cases} a(m+n)+b(m-n)=12 \\\\ b(m+n)+a(m-n)=16 \\end{cases}  $ ，则 $ m+2n= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B．2C．4D．6</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西陕西师范大学附属中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-28", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526524788237443072", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526524788237443072", "title": "陕西省西安市雁塔区陕西师范大学附属中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526524622658904064", "questionArticle": "<p>7．解方程组：</p><p>(1) $ \\begin{cases} x+3y=5 \\\\ 3x+y=-1 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3(2x-y)+4(x-2y)=43 \\\\ 2(3x-y)-3(x-y)=0 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024陕西西光中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-28", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526524615251763200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "526524615251763200", "title": "陕西省西安市新城区西光中学教育集团联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526524619165048832", "questionArticle": "<p>8．下列四组值中，是二元一次方程 $ x-2y=1 $ 的解的是<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/23/2/1/0/0/0/526524505692348420/images/img_6.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\">（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=0 \\\\ y=1 \\end{cases}  $ B． $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ C． $ \\begin{cases} x=1 \\\\ y=1 \\end{cases}  $ D． $ \\begin{cases} x=1 \\\\ y=0 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西光中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-28", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526524615251763200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "526524615251763200", "title": "陕西省西安市新城区西光中学教育集团联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526526703440535552", "questionArticle": "<p>9．重庆金沙天街某家蛋糕店推出了“流沙羊角”和“开心果羊角”两款特色蛋糕．</p><p>(1)购买1个“流沙羊角”和1个“开心果羊角”需要37元，购买1个“流沙羊角”和2个“开心果羊角”需要54元，求“流沙羊角”和“开心果羊角”的单价分別为多少元？</p><p>(2)国庆节当天，蛋糕店进行促销活动，将“流沙羊角”的单价降低了 $ 2m $ 元，“开心果半角”单价降低了 $ m $ 元，节日当天“流沙羊角”的销量是“开心果羊角”销量的1.2倍，且“流沙羊角”的销售额为960元，“开心果羊角”的销售额为750元，求 $ m $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆市第一中学校 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-27", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526526693877522432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526526693877522432", "title": "重庆一中2024−2025学年九年级上学期周末消化作业数学试卷", "paperCategory": 1}, {"id": "521703764689985536", "title": "重庆市南开中学校2024−2025学年九年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526523903306407936", "questionArticle": "<p>10．阅读与思考</p><p>【阅读理解】</p><p>我们把四个数<i>a</i>，<i>b</i>，<i>c</i>，<i>d</i>排成两行两列，记为 $ \\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix}  $ ，称为二阶行列式，规定它的运算法则为 $ \\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix} =ad-bc $ ．</p><p>小李同学在学习二元一次方程组的解法时，发现可以利用二阶行列式求解．例如：求二元一次方程组 $ \\begin{cases} 3x+2y=5， \\\\ 4x+6y=7 \\end{cases}  $ 的解．</p><p>解：记 $ D=\\begin{vmatrix} 3 & 2 \\\\ 4 & 6 \\end{vmatrix} =3\\times 6-2\\times 4=10 $ ， $ D{{}_{ x } }=\\begin{vmatrix} 5 & 2 \\\\ 7 & 6 \\end{vmatrix} =5\\times 6-2\\times 7=16 $ ，</p><p> $ D{{}_{ y } }=\\begin{vmatrix} 3 & 5 \\\\ 4 & 7 \\end{vmatrix} =3\\times 7-5\\times 4=1 $ ，则原方程组的解为 $ \\begin{cases} x=\\dfrac { D{{}_{ x } } } { D }=\\dfrac { 16 } { 10 }=\\dfrac { 8 } { 5 }， \\\\ y=\\dfrac { D{{}_{ y } } } { D }=\\dfrac { 1 } { 10 }. \\end{cases}  $ </p><p>【类比应用】</p><p>(1)若二阶行列式 $ \\begin{vmatrix} x & x+1 \\\\ 2 & 1 \\end{vmatrix} =1 $ ，求<i>x</i>的值；</p><p>(2)已知方程组 $ \\begin{cases} 3x+4y=2， \\\\ 2x-y=5 \\end{cases}  $ 利用二阶行列式求得 $ D=-11 $ ，请求 $ D{{}_{ x } } $ ， $ D{{}_{ y } } $ ，并写出该方程组的解．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16402|16426", "keyPointNames": "解一元一次方程|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526523894435454976", "questionFeatureName": "阅读材料题", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "526523894435454976", "title": "山西省太原市迎泽区太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 233, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 233, "timestamp": "2025-07-01T02:28:19.165Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}