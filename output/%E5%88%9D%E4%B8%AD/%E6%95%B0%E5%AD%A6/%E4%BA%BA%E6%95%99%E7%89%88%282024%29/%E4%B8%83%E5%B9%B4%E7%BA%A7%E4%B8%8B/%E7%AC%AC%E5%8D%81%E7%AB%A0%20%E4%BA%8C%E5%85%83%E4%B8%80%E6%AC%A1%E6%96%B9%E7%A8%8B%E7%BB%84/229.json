{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 228, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "527896740722155520", "questionArticle": "<p>1．计算与解方程</p><p>(1)计算： $ \\sqrt { 16 }+\\sqrt[3] { -64 }-\\sqrt { (-3){^{2}} }+|\\sqrt { 3 }-1| $ ；</p><p>(2)解方程组： $ \\begin{cases} 4a-3b=11 \\\\ \\dfrac { a } { 3 }+\\dfrac { b-1 } { 4 }=\\dfrac { 13 } { 6 } \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南河南实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16256|16379|16424", "keyPointNames": "化简绝对值|二次根式的性质和化简|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527896733096910848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527896733096910848", "title": "河南省实验中学2024−2025学年八年级上学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527896739883294720", "questionArticle": "<p>2．对于<i>x</i>，<i>y</i>定义一种新运算“*”： $ x*y=ax{ { + } }by $ ，其中<i>a</i>，<i>b</i>为常数，等式右边是通常的加法和乘法运算．已知： $ 3*5=15 $ ， $ 4*7=28 $ ，那么 $ 1*2= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南河南实验中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16278|16426", "keyPointNames": "有理数的混合运算|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527896733096910848", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527896733096910848", "title": "河南省实验中学2024−2025学年八年级上学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527141556341481472", "questionArticle": "<p>3．用加减法解方程组 $ \\begin{cases} x+y=-3① \\\\ 3x+y=6② \\end{cases}  $ 由②-①消去未知数 $ y $ ，所得到的一元一次方程是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2x=9 $ B． $ 2x=3 $ C． $ 4x=9 $ D． $ 4x=3 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南平顶山 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-01-03", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141551950045184", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "527141551950045184", "title": "河南省平顶山市等2地2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "215581378011766784", "title": "2022年七年级下册浙教版数学第2章2.3解二元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527897143295647744", "questionArticle": "<p>4．在“新冠病毒”疫情防控期间，某药店分两次购进酒精消毒液与测温枪进行销售，两次购进同一商品的进价相同，具体情况如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>购进数量（件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>购进所需费用（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>酒精消毒液</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>测温枪</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">6300</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4900</p></td></tr></table><p>(1)求酒精消毒液和测温枪每件的进价分别是多少元？</p><p>(2)该药店决定酒精消毒液以每件15元出售，测温枪以每件200元出售．为满足市场需求．需购进这两种商品共1000件，设购进测温枪<i>m</i>件，获得的利润为<i>W</i>元，请求出获利<i>W</i>（元）与购进测温枪件数<i>m</i>（件）之间的函数关系式．若测温枪的数量不超过300件，求该公司销售完上述1000件商品获得的最大利润．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江西吉安 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 3, "createTime": "2025-01-03", "keyPointIds": "16441|16544", "keyPointNames": "其他问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527583381254610944", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527583381254610944", "title": "江西省吉安市2024−2025学年八年级上学期12月考数学试题", "paperCategory": 1}, {"id": "527897134026235904", "title": "江西省吉安市八校联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "177508048298090496", "title": "江西省九江市2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526894206117257216", "questionArticle": "<p>5．阅读材料&nbsp;&nbsp;小强同学在解方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} x+y+3=10 \\\\ 4(x+y)-y=25 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ 时发现，可将第一个方程通过移项变形为 $ x+y= $  $ 7 $ ，然后把第二个方程中的 $ x+y $ 换成7，可以很轻松地解出这个方程组．小强同学发现的这种方法叫作“整体代入法”，是中学数学里很常用的一种解题方法．</p><p>(1)请按照小强的解法解出这个方程组；</p><p>(2)用整体代入法解方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 2x+3y=-4 \\\\ 6x-5y=16 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "questionFeatureName": "阅读材料题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526894205571997696", "questionArticle": "<p>6．某校组织“大手拉小手，义卖献爱心”活动，计划购买黑白两种颜色的文化衫进行手绘设计后出售，并将所获利润全部捐给山区困难孩子．已知该学校从批发市场花3600元购买了黑白两种颜色的文化衫200件．每件文化衫的批发价及手绘后的零售价如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">批发价（元）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">零售价（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">黑色文化衫</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">35</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">白色文化衫</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">15</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">25</p></td></tr></table><p>假设通过手绘设计后全部售出，求该校这次义卖活动所获利润．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16437|16440", "keyPointNames": "销售利润问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526895273160777728", "questionArticle": "<p>7．一批货物要运往某地，货主准备租用汽车运输公司的甲、乙两种货车，已知过去两次租用这两种货车的运货情况如下表:</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第二次</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>甲种货车的辆数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>2辆</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>5辆</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>乙种货车的辆数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>3辆</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>6辆</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>累计运货重量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>14吨</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>32吨</p></td></tr></table><p>(1)分别求甲乙两种货车每辆载重多少吨?</p><p>(2)现租用该公司3辆甲种货车和5辆乙种货车刚好一次运完这批货物，如果按每吨付运费120元计算，货主应付运费多少元?</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-03", "keyPointIds": "16424|16440", "keyPointNames": "加减消元法解二元一次方程组|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526895263329329152", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526895263329329152", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末数学模拟测试题一", "paperCategory": 1}, {"id": "399279467166736384", "title": "四川省成都市双流区成都棠湖外国语学校2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526894201142812672", "questionArticle": "<p>8．已知 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} x=1 \\\\ y=2 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ 是关于<i>x</i>，<i>y</i>的方程 $ 3x-ky=1 $ 的一个解，则<i>k</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2 $　　　　B．1　　　　C．2　　　　D．7</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526894204192071680", "questionArticle": "<p>9．小亮、小红和笑笑三个人玩飞镖游戏，各投6支飞镖，规定在同一圆环内得分相同，三人中靶和得分情况如图，则小红得分为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>分．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/04/2/20/0/0/0/530742623713140736/images/img_1.png\" style='vertical-align:middle;' width=\"237\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "526894204980600832", "questionArticle": "<p>10．解方程组：</p><p>(1) $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} y=x-1 \\\\ 5x+2y=5 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ ；</p><p>(2) $ \\begin{cases} 2x-3y=6 \\\\ 3x+2y=22 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894197783175168", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894197783175168", "title": "山西省太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 229, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 229, "timestamp": "2025-07-01T02:27:48.242Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}