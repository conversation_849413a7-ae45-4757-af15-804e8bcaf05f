{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 216, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "536617636051853312", "questionArticle": "<p>1．某商场从厂家购进了<i>A</i>、<i>B</i>两种品牌篮球，第一批购买了这两种品牌篮球各40个，共花费了7200元．全部销售完后，商家打算再购进一批这两种品牌的篮球，最终第二批购进50个<i>A</i>品牌篮球和30个<i>B</i>品牌篮球共花费了7400元．两次购进<i>A</i>、<i>B</i>两种篮球进价保持不变．</p><p>(1)求<i>A</i>、<i>B</i>两种品牌篮球进价各为多少元一个；</p><p>(2)第二批次篮球在销售过程中，<i>A</i>品牌篮球每个原售价为140元，售出40个后出现滞销，商场决定打折出售剩余的<i>A</i>品牌篮球；<i>B</i>品牌篮球每个按进价加价30%销售，很快全部售出．已知第二批次两种品牌篮球全部售出后共获利2440元，求<i>A</i>品牌篮球打几折出售？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南娄底 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 3, "createTime": "2025-01-21", "keyPointIds": "16402|16406|16424|16438", "keyPointNames": "解一元一次方程|销售盈亏问题|加减消元法解二元一次方程组|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617627818434560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536617627818434560", "title": "湖南省娄底市冷水江市2024−2025学年七年级上学期期末考试数学试题", "paperCategory": 1}, {"id": "376129623602536448", "title": "重庆市沙坪坝区第一中学校2023-2024学年八年级上学期10月月考数学试题", "paperCategory": 1}, {"id": "426148560888963072", "title": "重庆市九龙坡区重庆实验外国语学校2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534867385620144128", "questionArticle": "<p>2．某单车安装公司需要安装一批共享单车，由于抽调不出足够熟练的工人，准备招聘一批新工人．已知2 名熟练工人和3 名新工人每天共安装44 辆共享单车；4 名熟练工人每天安装的共享单车数与5名新工人每天安装的共享单车数一样多．求每名熟练工人和新工人每天分别可以安装多少辆共享单车?</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534867377508360192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534867377508360192", "title": "陕西省榆林市高新区2024—2025学年上学期八年级期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534867381727830016", "questionArticle": "<p>3．地理老师介绍：长江比黄河长836千米，黄河长度的6倍比长江长度的5倍多1284千米．小东为了求出长江和黄河的长度，设长江长为 $ x $ 千米，黄河长为 $ y $ 千米，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=836 \\\\ 6y-5x=1284 \\end{cases}  $ B． $ \\begin{cases} x-y=836 \\\\ 6x-5y=1284 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=836 \\\\ 6y-5x=1284 \\end{cases}  $ D． $ \\begin{cases} x+y=836 \\\\ 6y+5x=1284 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东江门 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056986691907584", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056986691907584", "title": "广东省江门市广雅中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "534867377508360192", "title": "陕西省榆林市高新区2024—2025学年上学期八年级期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "534867384307326976", "questionArticle": "<p>4．解方程组： $ \\begin{cases} 3x-y=1① \\\\ x+y=3② \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534867377508360192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534867377508360192", "title": "陕西省榆林市高新区2024—2025学年上学期八年级期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534867380939300864", "questionArticle": "<p>5．若 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是关于 $ x $ ， $ y $ 的二元一次方程 $ ax-y=1 $ 的解，则 $ a $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B．1C． $ -1 $ D． $ -2 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534867377508360192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534867377508360192", "title": "陕西省榆林市高新区2024—2025学年上学期八年级期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "534866557727449088", "questionArticle": "<p>6．我校学生组织冬游活动，交通工具有两座车和五座车两种，两座车每人每次18元，五座车每人每次8元，共100名学生参与了活动，乘坐了两种车若干，且每辆车正好坐满．</p><p>(1)若一共花去车费1300元，则两种车各租用了多少辆？（列二元一次方程组解决问题）</p><p>(2)因场地停车位置有限，只能停靠24辆车．故新提供了大巴车可选择，每辆大巴车可乘坐7人．若每种车型必须都租用，请你设计符合要求的租车方案．</p><p>(3)若每辆大巴车的租金为30元一次，请你通过计算，找出租金最低的租车方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-20", "keyPointIds": "16420|16441", "keyPointNames": "二元一次方程的解|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534866541696819200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534866541696819200", "title": "安徽省淮北市部分学校2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534866553688334336", "questionArticle": "<p>7．解方程（组）：</p><p>(1) $ x-\\dfrac { x+1 } { 2 }=2+\\dfrac { x-2 } { 3 } $ </p><p>(2) $ \\begin{cases} 3x+2y=11① \\\\ 3x-5y=4② \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025安徽淮北 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-20", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534866541696819200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534866541696819200", "title": "安徽省淮北市部分学校2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534866550869762048", "questionArticle": "<p>8．列二元一次方程组解应用题：如图，在大长方形中，放置6个形状、大小都相同的小长方形，则阴影部分的面积之和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/15/2/1/0/0/0/534866525238370312/images/img_9.png\" style=\"vertical-align:middle;\" width=\"212\" alt=\"试题资源网 https://stzy.com\"></p><p>A．34B．43C．50D．54</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-20", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534866541696819200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534866541696819200", "title": "安徽省淮北市部分学校2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "534114411373436928", "questionArticle": "<p>9．《九章算术》是中国传统数学最重要的著作，奠定了中国传统数学的基本框架．它的代数成就主要包括开方术、正负术和方程术．其中，方程术是《九章算术》最高的数学成就．《九章算术》中记载：“今有牛五、羊二，直金十两；牛二、羊五，直金八两．问：牛、羊各直金几何？”译文：“假设有5头牛、2只羊，值金10两；2头牛、5只羊，值金8两．问：每头牛、每只羊各值金多少两？”设每头牛值金<i>x</i>两，每只羊值金<i>y</i>两，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/13/2/1/0/0/0/534114361209561090/images/img_13.png\" style=\"vertical-align:middle;\" width=\"96\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 5x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $ B． $ \\begin{cases} 2x+5y=10 \\\\ 5x+2y=8 \\end{cases}  $ </p><p>C． $ \\begin{cases} \\dfrac { 1 } { 5 }x+\\dfrac { 1 } { 2 }y=10 \\\\ \\dfrac { 1 } { 2 }x+\\dfrac { 1 } { 5 }y=8 \\end{cases}  $ D． $ \\begin{cases} \\dfrac { 1 } { 2 }x+\\dfrac { 1 } { 5 }y=10 \\\\ \\dfrac { 1 } { 5 }x+\\dfrac { 1 } { 2 }y=8 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆渝北 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534114404826128384", "questionFeatureName": "数学文化题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534114404826128384", "title": "重庆市渝北区六校联盟2024−2025学年九年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "535215591990272000", "questionArticle": "<p>10．如图，在 $ { \\rm{ R } }{ \\rm{ t } }\\mathrm{ △ }ABC $ 中， $ \\angle ACB\\mathrm{ = }90{}\\degree  $ ， $ AC=8{ \\rm{ ， } }BC=6 $ ，动点<i>P</i>在 $ CA $ 上从点<i>C</i>向终点<i>A</i>匀速运动，同时，动点<i>Q</i>在 $ AB $ 上从点<i>A</i>向终点<i>B</i>匀速运动，它们同时到达终点．设 $ CP=x $ ， $ BQ=y $ ，则<i>y</i>关于<i>x</i>的函数表达式是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/16/2/1/0/0/0/535215552601563137/images/img_22.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东青岛 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-20", "keyPointIds": "16430|16672", "keyPointNames": "行程问题|勾股定理", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "535215586042748928", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "535215586042748928", "title": "2024年山东省青岛市市北区部分中学中考数学一模试题", "paperCategory": 1}], "questionTypeCode": "5"}]}}, "requestData": {"pageNum": 217, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 217, "timestamp": "2025-07-01T02:26:22.952Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}