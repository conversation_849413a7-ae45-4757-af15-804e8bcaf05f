{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 233, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "526523902425604096", "questionArticle": "<p>1．太原五中计划购置篮球、钢笔、笔记本作为期末奖品，采购员小琪在某文体用品店购买完毕回到学校后发现发票被弄花了，有几个数据变得不清楚，如图所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>货物或应税劳务、服务名称</p><p style=\"text-indent:28pt;\">篮球</p><p style=\"text-indent:28pt;\">钢笔</p><p style=\"text-indent:28pt;\">笔记本</p><p style=\"text-indent:28pt;\">合计</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">规格型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">单位</p><p style=\"text-align:center;\">个</p><p style=\"text-align:center;\">支</p><p style=\"text-align:center;\">本</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">数量</p><p style=\"text-align:center;\">6</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/30/2/20/0/0/0/528924414806630401/images/img_1.png\" style='vertical-align:middle;' width=\"46\" alt=\"试题资源网 https://stzy.com\"></p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/30/2/20/0/0/0/528924414806630402/images/img_2.png\" style='vertical-align:middle;' width=\"46\" alt=\"试题资源网 https://stzy.com\"></p><p style=\"text-align:center;\">46</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">单价</p><p style=\"text-align:center;\">100.00</p><p style=\"text-align:center;\">15.00</p><p style=\"text-align:center;\">5.00</p><p style=\"text-align:center;\"></p><p style=\"text-align:center;\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">金额</p><p style=\"text-align:center;\">600.00</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/30/2/20/0/0/0/528924414806630403/images/img_3.png\" style='vertical-align:middle;' width=\"69\" alt=\"试题资源网 https://stzy.com\"></p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/30/2/20/0/0/0/528924414806630404/images/img_4.png\" style='vertical-align:middle;' width=\"69\" alt=\"试题资源网 https://stzy.com\"></p><p style=\"text-align:center;\">900.0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">税率</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">税额</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>价税合计（大写）</p></td><td colspan=\"7\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\otimes  $   玖佰元整                               （小写）900.00</p></td></tr></table><p>请根据发票中现有的信息，帮助小琪复原弄花的数据，即分别求出购置钢笔、笔记本的数量及对应的金额．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526523894435454976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526523894435454976", "title": "山西省太原市迎泽区太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526523901897121792", "questionArticle": "<p>2．解方程组：</p><p>(1) $ \\begin{cases} 3x-y=6 \\\\ x-3y=2 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { 1 } { 2 }x+4y=27 \\\\ x-\\dfrac { 1 } { 3 }y=4 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526523894435454976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526523894435454976", "title": "山西省太原市迎泽区太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526523900169068544", "questionArticle": "<p>3．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x+3y=4-a \\\\ x-y=3a \\end{cases}  $ ，给出下列结论中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①当这个方程组的解 $ x $ ， $ y $ 的值互为相反数时， $ a=-2 $ ；</p><p>②当 $ a=1 $ 时，方程组的解也是方程 $ x+y=4+2a $ 的解；</p><p>③无论 $ a $ 取什么实数， $ x+2y $ 的值始终不变；</p><p>④若用 $ x $ 表示 $ y $ ，则 $ y=-\\dfrac { x } { 2 }+\\dfrac { 3 } { 2 } $ ．</p><p>A．①②　　　　B．②③　　　　C．①③④　　　　D．②③④</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526523894435454976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526523894435454976", "title": "山西省太原市迎泽区太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526523899841912832", "questionArticle": "<p>4．某公司去年的利润（总产值 $ - $ 总支出）为300万元，今年总产值比去年增加了 $ 20\\% $ ，总支出比去年减少了 $ 10\\% $ ，今年的利润为980万元，如果去年的总产值<i>x</i>万元，总支出<i>y</i>万元，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=300 \\\\ \\left ( { 1+20{ \\rm{ \\% } } } \\right ) x-\\left ( { 1-10{ \\rm{ \\% } } } \\right ) y=980 \\end{cases}  $　　　　B． $ \\begin{cases} x-y=300 \\\\ \\left ( { 1-20{ \\rm{ \\% } } } \\right ) x-\\left ( { 1+10{ \\rm{ \\% } } } \\right ) y=980 \\end{cases}  $</p><p>C． $ \\begin{cases} x-y=300 \\\\ 20{ \\rm{ \\% } }x-10{ \\rm{ \\% } }y=980 \\end{cases}  $　　　　D． $ \\begin{cases} x-y=300 \\\\ \\left ( { 1-20{ \\rm{ \\% } } } \\right ) x-\\left ( { 1-10{ \\rm{ \\% } } } \\right ) y=980 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西太原师范学院附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526523894435454976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526523894435454976", "title": "山西省太原市迎泽区太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527584885965365248", "questionArticle": "<p>5．随着“低碳生活，绿色出行”理念的普及，某汽车销售公司计划购进一批新能源汽车尝试进行销售，据了解，2辆 $ A $ 型汽车,3辆 $ B $ 型汽车的进价共计80万元；3辆 $ A $ 型汽车,2辆 $ B $ 型汽车的进价共计95万元．</p><p>(1)求 $ A $ , $ B $ 两种型号的汽车每辆进价分别为多少万元？</p><p>(2)若该公司计划正好用180万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），则该公司有哪几套方案？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西汉中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527584876645621760", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527584876645621760", "title": "陕西省汉中市2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527584883306176512", "questionArticle": "<p>6．解方程组： $ \\begin{cases} 3x+2y=11① \\\\ 3x-5y=4② \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024陕西汉中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527584876645621760", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527584876645621760", "title": "陕西省汉中市2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527584882580561920", "questionArticle": "<p>7．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 2x+5y=k \\\\ x-4y=15 \\end{cases}  $ 的解满足 $ x+y=0 $ ，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西汉中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527584876645621760", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527584876645621760", "title": "陕西省汉中市2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527584881590706176", "questionArticle": "<p>8．对于实数 $ x $ ， $ y $ 定义新运算： $ x*y=ax+by+5 $ ，其中 $ a $ ， $ b $ 为常数．已知 $ 1*2=9 $ ， $ \\left ( { -3 } \\right ) *3=2 $ ，则 $ 3*4 $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．14　　　　B．15　　　　C．13　　　　D．11</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西汉中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527584876645621760", "questionFeatureName": "新定义问题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527584876645621760", "title": "陕西省汉中市2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527584881129332736", "questionArticle": "<p>9．我国古代数学著作《九章算术》记载了一道“牛马问题”：“今有二马、一牛价过一万，如半马之价．一马、二牛价不满一万，如半牛之价．问牛、马价各几何．”其大意为：现有两匹马加一头牛的价钱超过一万，超过的部分正好是半匹马的价钱；一匹马加上二头牛的价钱则不到一万．不足部分正好是半头牛的价钱，求一匹马、一头牛各多少钱？设一匹马价钱为 $ x $ 元，一头牛价钱为 $ y $ 元，则符合题意的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+y-10000=\\dfrac { x } { 2 } \\\\ 10000-\\left ( { x+2y } \\right ) =\\dfrac { y } { 2 } \\end{cases}  $　　　　B． $ \\begin{cases} 2x+y-10000=\\dfrac { x } { 2 } \\\\ x+2y-10000=\\dfrac { y } { 2 } \\end{cases}  $</p><p>C． $ \\begin{cases} 2x+y+10000=\\dfrac { x } { 2 } \\\\ x+2y-10000=\\dfrac { y } { 2 } \\end{cases}  $　　　　D． $ \\begin{cases} 2x+y+10000=\\dfrac { x } { 2 } \\\\ 10000-\\left ( { x+2y } \\right ) =\\dfrac { y } { 2 } \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西汉中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527584876645621760", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527584876645621760", "title": "陕西省汉中市2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527584039986831360", "questionArticle": "<p>10．某商场购进<i>A</i>，<i>B</i>两种商品，已知购进3件<i>A</i>商品和5件<i>B</i>商品费用相同，购进3件<i>A</i>商品和1件<i>B</i>商品总费用为360元．</p><p>(1)求<i>A</i>，<i>B</i>两种商品每件进价各为多少元？（列方程或方程组求解）</p><p>(2)若该商场计划购进<i>A</i>，<i>B</i>两种商品共80件，其中<i>A</i>商品<i>m</i>件．若<i>A</i>商品按每件150元销售，<i>B</i>商品按每件80元销售，求销售完<i>A</i>，<i>B</i>两种商品后获得总利润<i>w</i>（元）与<i>m</i>（件）的函数关系式．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2022四川雅安 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2024-12-27", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207249721957588992", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "207249721957588992", "title": "四川省雅安市2022年中考数学真题", "paperCategory": 1}, {"id": "527584030084079616", "title": "山东省济南外国语学校2024−2025学年上学期12月份月考八年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 234, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 234, "timestamp": "2025-07-01T02:28:25.955Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}