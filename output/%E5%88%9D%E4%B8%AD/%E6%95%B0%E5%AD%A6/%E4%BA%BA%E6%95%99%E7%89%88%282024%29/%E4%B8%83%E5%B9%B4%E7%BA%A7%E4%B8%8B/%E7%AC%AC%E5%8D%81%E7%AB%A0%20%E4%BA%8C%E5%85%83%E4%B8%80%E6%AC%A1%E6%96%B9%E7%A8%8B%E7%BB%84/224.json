{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 223, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "532701693672202240", "questionArticle": "<p>1．甲、乙两地相距74千米，途中有上坡、平路和下坡．一汽车从甲地下午1点出发到乙地是下午3点30分，停留30分钟后从乙地出发，6点48分返回甲地．已知汽车在上坡路每小时行驶20千米，平路每小时行驶30千米，下坡每小时行驶40千米，求甲地到乙地的行驶过程中平路、上坡、下坡分别是多少千米？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-12", "keyPointIds": "16424|16430", "keyPointNames": "加减消元法解二元一次方程组|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532701692300664832", "questionArticle": "<p>2．如图，10块完全相同的小长方形墙砖拼成一个大长方形，则小长方形的面积为 <u>&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/09/2/1/0/0/0/532701664244965378/images/img_3.png\" style=\"vertical-align:middle;\" width=\"187\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-12", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "532701693147914240", "questionArticle": "<p>3．解方程（或方程组）：</p><p>(1) $ \\dfrac { 4x+1 } { 3 }-\\dfrac { 2x-4 } { 6 }=-1 $ ；</p><p>(2) $ \\begin{cases} 3x+y=22 \\\\ 4\\left ( { x+y } \\right ) -5\\left ( { x-y } \\right ) =2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-12", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532701692011257856", "questionArticle": "<p>4．对于有理数<i>x</i>，<i>y</i>定义新运算： $ x*y=ax+by-5 $ ，其中<i>a</i>，<i>b</i>为常数已知 $ 1*2=-9 $ ， $ \\left ( { -3 } \\right ) *3=-2 $ ，则 $ a-b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-12", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "532701691235311616", "questionArticle": "<p>5．已知关于 $ x,y $ 的二元一次方程组 $ \\begin{cases} 3x-y=4m+1 \\\\ x+y=2m-5 \\end{cases}  $ 的解满足 $ x-y=4 $ ，则<i>m</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．−1B．7C．1D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北廊坊 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-12", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454404010844160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588454404010844160", "title": "河北省廊坊市第四中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}, {"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "532700560073465856", "questionArticle": "<p>6．【阅读理解】</p><p>在求代数式的值时，有些题目可以用整体求值的方法，化难为易．</p><p>例：已知 $ \\begin{cases} 3x+2y+z=4 & ① \\\\ 7x+4y+3z=10 & ② \\end{cases}  $ ，求 $ 2x+y+z $ 的值．</p><p>解： $ { \\rm{ ② } }-{ \\rm{ ① } } $ 得 $ 4x+2y+2z=6 $ ③</p><p> $ ③\\times \\dfrac { 1 } { 2 } $ 得 $ 2x+y+z=3 $ ，</p><p>所以， $ 2x+y+z $ 的值为 $ 3 $ ．</p><p>【类比迁移】</p><p>（1）已知 $ \\begin{cases} x+2y+3z=10① \\\\ 5x+6y+7z=2② \\end{cases}  $ 求 $ 3x+4y+5z $ 的值；</p><p>【实际应用】</p><p>（2）某班级班委准备把本学期卖废品的钱给同学们买期中奖品，根据商店的价格，若购买 $ 3 $ 本笔记本、 $ 2 $ 支签子笔、 $ 1 $ 支记号笔需要 $ 28 $ 元；若购买 $ 7 $ 本笔记本、 $ 5 $ 支签字笔、 $ 3 $ 支记号笔需要 $ 66 $ 元；本班共 $ 45 $ 位同学，则购买 $ 45 $ 本笔记本、 $ 45 $ 支签字笔、 $ 45 $ 支记号笔需要多少钱？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-11", "keyPointIds": "16424|16438", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532700550288154624", "questionFeatureName": "阅读材料题", "questionMethodName": "整体思想", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "532700550288154624", "title": "四川省达州市渠县中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532700559809224704", "questionArticle": "<p>7．某中学组织师生共 $ 480 $ 人去参观博物院，阅读下列对话：</p><p>李老师：“客运公司有 $ 60 $ 座和 $ 45 $ 座两种型号的客车可供租用，且租用 $ 1 $ 辆 $ 60 $ 座客车和 $ 1 $ 辆 $ 45 $ 座客车到河南省博物院，一天的租金共计 $ 1800 $ 元．”</p><p>小明说：“我们学校八年级师生昨天在这个客运公司租了 $ 4 $ 辆 $ 60 $ 座和 $ 3 $ 辆 $ 45 $ 座的客车到河南省博物院，一天的租金共计 $ 6400 $ 元．”</p><p>(1)客运公司 $ 60 $ 座和 $ 45 $ 座的客车每辆每天的租金分别是多少元？</p><p>(2)若同时租用两种或一种客车，要使每位师生都有座位且每辆客车恰好坐满，共有哪几种租车方式？其中最省钱的租车方式租车费用为多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-11", "keyPointIds": "16420|16434", "keyPointNames": "二元一次方程的解|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532700550288154624", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "532700550288154624", "title": "四川省达州市渠县中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532700558425104384", "questionArticle": "<p>8．解二元一次方程组：</p><p>(1) $ \\begin{cases} x=3y, \\\\ 4x+y=13; \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x+5y=-9① \\\\ 2x-3y=13② \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024四川渠中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-01-11", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532700550288154624", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "532700550288154624", "title": "四川省达州市渠县中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532700557154230272", "questionArticle": "<p>9．《九章算术》是我国东汉年间的数学经典著作，在“方程”一章里二元一次方程组是由算筹布置而成的．算筹图是竖排的，为看图方便，我们把它改为横排．如图1，各行从左到右列出的算筹数分别表示未知数<i>x</i>，<i>y</i>的系数与方程中的常数项，以方程组的形式表述出来就是 $ \\begin{cases} 3x+2y=19 \\\\ x+4y=23 \\end{cases}  $ ，类似地，图2所示的算筹图可以用方程组表述为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/14/2/20/0/0/0/534382656873603072/images/img_1.jpg\" style='vertical-align:middle;' width=\"269\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-11", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532700550288154624", "questionFeatureName": "阅读材料题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "532700550288154624", "title": "四川省达州市渠县中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "532315588602929152", "questionArticle": "<p>10．已知<i>x</i>、<i>y</i>、<i>z</i>满足 $ \\begin{cases} 5x+y-4z=0 \\\\ 9x-y-3z=0 \\end{cases}  $ ，则 $ x:y:z= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-10", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532315579505483776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532315579505483776", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（二）", "paperCategory": 1}], "questionTypeCode": "5"}]}}, "requestData": {"pageNum": 224, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 224, "timestamp": "2025-07-01T02:27:12.395Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}