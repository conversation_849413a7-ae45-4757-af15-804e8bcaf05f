{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 198, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544317663209627648", "questionArticle": "<p>1．《九章算术》是中国古代重要的数学著作，其中“盈不足术”记载：今有共买鸡，人出九，盈一十一；人出六，不足十六，问：人数、鸡价各几何?译文：今有人合伙买鸡，每人出 $ 9 $ 钱，会多出十一钱；每人出 $ 6 $ 钱，又差 $ 16 $ 钱，问人数、买鸡的钱各是多少 $ ? $ 设人数为 $ x $ ，买鸡的钱数为 $ y $ ，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 9x+11=y \\\\ 6x+16=y \\end{cases}  $ B． $ \\begin{cases} 9x-11=y \\\\ 6x-16=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 9x+11=y \\\\ 6x-16=y \\end{cases}  $ D． $ \\begin{cases} 9x-11=y \\\\ 6x+16=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317661406076928", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317661406076928", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317957674934272", "questionArticle": "<p>2．某班级开展知识竞赛活动，去咖啡店购买A、B两种款式的咖啡作为奖品.若买10杯A款咖啡，15杯B款咖啡共需230元；若买25杯A款咖啡，25杯B款咖啡共需450元.为了满足市场的需求，咖啡店推出每杯2元的加料服务，顾客在选完款式后可以自主选择加料或者不加料.小华恰好用了208元购买A、B两款咖啡，其中A款不加料的杯数是总杯数的 $ \\dfrac{1}{3} $ .</p><p>（1） 在不加料的情况下，A款咖啡和B款咖啡的销售单价各是多少？</p><p>（2） 在不加料的情况下，购买A、B两种款式的咖啡（两种都要）刚好花费200元，那么有几种购买方案？</p><p>（3） 小华购买的这两款咖啡，其中B款加料的咖啡买了多少杯？（直接写出答案）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317735301324800", "questionArticle": "<p>3．数学老师要求同学们列二元一次方程组解决问题：在某市“精准扶贫”工作中，甲、乙两个工程队先后为扶贫村修建了3 000米的村路，甲工程队每天修建150米，乙工程队每天修建200米，共用18天完成.求甲、乙两个工程队分别修建了多少天.</p><p>（1） 张红同学根据题意，列出了二元一次方程组 $ \\begin{cases}x+y=3000,\\\\ \\dfrac{x}{150}+\\dfrac{y}{200}=18,\\end{cases} $ 那么这个方程组中未知数 $ x $ 表示的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，未知数 $ y $ 表示的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2） 李芳同学设甲工程队修建了 $ p $ 天，乙工程队修建了 $ q $ 天.请你按照她的思路解答老师的问题.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317735037083648", "questionArticle": "<p>4．【综合实践】</p><p>主题：制作一个有盖长方体盒子.</p><p>操作：如图所示，长方形纸片 $ ABCD $ 中， $ AB=4\\mathrm{d}\\mathrm{m} $ ， $ AD=6\\mathrm{d}\\mathrm{m} $ ，剪掉阴影部分后，剩下的纸片可折成一个底面是正方形的有盖长方体盒子.</p><p>计算：求这个有盖长方体盒子的高和底面正方形的边长.</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/11/2/1/0/0/0/544317717475532802/images/img_4.jpg\" style=\"vertical-align:middle;\" width=\"185\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "questionFeatureName": "综合与实践题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317957343584256", "questionArticle": "<p>5．小林在某商店买商品A，B共三次，只有一次购买时，商品A，B同时打折，其余两次均按标价购买，三次购买商品A，B的数量和费用如下表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:414.7pt; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.05pt; vertical-align: top;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>购买商品A的数量（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>购买商品B的数量（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 118.8pt; vertical-align: top;\"><p>购买总费用（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.05pt; vertical-align: top;\"><p>第一次购物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 118.8pt; vertical-align: top;\"><p>1 140</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.05pt; vertical-align: top;\"><p>第二次购物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>7</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 118.8pt; vertical-align: top;\"><p>1 110</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.05pt; vertical-align: top;\"><p>第三次购物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>9</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.9pt; vertical-align: top;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 118.8pt; vertical-align: top;\"><p>1 062</p></td></tr></table><p>（1） 小林按折扣价购买商品A，B是第<u>&nbsp;&nbsp;</u>次购物；</p><p>（2） 求商品A，B的标价；</p><p>（3） 若商品A，B的折扣相同，则商店是打几折出售这两种商品的？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16437|16440", "keyPointNames": "销售利润问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317734735093760", "questionArticle": "<p>6．小明和小亮做两个数的加法游戏，小明在一个加数后面多写了一个0，得到的和为242；而小亮在另一个加数后面多写了一个0，得到的和为341，原来两个加数中较小的加数是<u>&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317734470852608", "questionArticle": "<p>7．如图所示，一个长方形恰好分成6个正方形，其中最小的正方形的边长是1，则这个长方形的面积是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/11/2/1/0/0/0/544317717475532801/images/img_3.jpg\" style=\"vertical-align:middle;\" width=\"98\" alt=\"试题资源网 https://stzy.com\"></p><p>A．143B．168C．363D．572</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317957012234240", "questionArticle": "<p>8．李师傅从杭州驾车到椒江办事，汽车在高速路段平均油耗为6升/百公里（100公里油耗为6升），在非高速路段平均油耗为7.5升/百公里，从杭州到椒江的总油耗为16.5升，总路程为270公里.（注：1公里 $ =1 $ 千米）</p><p>（1） 求此次杭州到椒江高速路段的路程；</p><p>（2） 若汽油价格为8元/升，高速路段过路费为0.45元/公里，求此次杭州到椒江的单程交通费用（交通费用 $ = $ 油费 $ + $ 过路费）.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317734206611456", "questionArticle": "<p>9．如图所示，把40张形状、大小完全相同的小长方形（长是宽的2倍）卡片既不重叠又无空隙地放在一个底面为长方形（长与宽的比为 $ 5:3 $ ）的盒子底部边沿，则盒子底部未被卡片覆盖的长方形的长与宽的比为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/11/2/1/0/0/0/544317717475532800/images/img_2.jpg\" style=\"vertical-align:middle;\" width=\"89\" alt=\"试题资源网 https://stzy.com\"></p><p>A．  $ 8:5 $ B．  $ 3:2 $ C． $ 5:4 $ D． $ 5:3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317956752187392", "questionArticle": "<p>10．商场购进A、B、C三种商品各100件、112件、60件，分别按照 $ 25\\mathrm{\\%} $ 、 $ 40\\mathrm{\\%} $ 、 $ 60\\mathrm{\\%} $ 的利润进行标价，其中商品C的标价为80元.为了促销，商场举行优惠活动：如果同时购买A、B商品各两件，就免费获赠一件C商品，这个优惠活动，实际上相当于这五件商品打了七五折．那么，商场购进这三种商品一共花了<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 199, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 199, "timestamp": "2025-07-01T02:24:21.846Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}