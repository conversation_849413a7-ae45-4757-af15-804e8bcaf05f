{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 230, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "529301066485112832", "questionArticle": "<p>1．解方程组① $ \\begin{cases}y=x-3,\\\\ 7x+5y=-9,\\end{cases} $ ② $ \\begin{cases}3x+5y=12,\\\\ 3x-15y=-6,\\end{cases} $ 比较简便的方法是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．都用代入消元法</p><p>B．都用加减消元法</p><p>C．①用代入消元法,②用加减消元法</p><p>D．①用加减消元法,②用代入消元法</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "九年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2024-12-31", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "529301065306513408", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "529301065306513408", "title": "第二章 方程(组)与不等式(组) 第1课时 一次方程(组)—题型指导（诊断）", "paperCategory": 10}, {"id": "140857636258357248", "title": "河北省唐山市开平区2020年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527900289073979392", "questionArticle": "<p>2．阅读感悟：</p><p>有些关于方程组的问题，欲求的结果不是每一个未知数的值，而是关于未知数的代数式的值，如以下问题：</p><p>已知实数 $ x $ ， $ y $ 满足 $ 3x-y=5 $ ①， $ 2x+3y=7 $ ②，求 $ x-4y $ 和 $ 7x+5y $ 的值．</p><p>本题常规思路是将①②两式联立组成方程组，解得 $ x $ ， $ y $ 的值再代入要求值的代数式得到答案，常规思路运算量比较大，其实，仔细观察两个方程未知数的系数之间的关系，本题还可以通过适当变形整体求得代数式的值，如由 $ ①-② $ 可得 $ x-4y=-2 $ ，由 $ ①+②\\times 2 $ 可得 $ 7x+5y=19 $ ．这样的解题思想就是通常所说的“整体思想”．</p><p>解决问题：</p><p>(1)已知二元一次方程组 $ \\begin{cases} 2x+y=7 \\\\ x+2y=8 \\end{cases}  $ ，则 $ x-y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ x+y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)某班级组织活动购买小奖品，买 $ 20 $ 支铅笔， $ 3 $ 块橡皮， $ 2 $ 本日记本共需 $ 32 $ 元，买 $ 39 $ 支铅笔， $ 5 $ 块橡皮， $ 3 $ 本日记本共需 $ 58 $ 元，则购买 $ 1 $ 支铅笔， $ 1 $ 块橡皮， $ 1 $ 本日记本共需多少元？</p><p>(3)对于实数 $ x $ ， $ y $ ，定义新运算： $ x*y=ax+by+c $ ，其中 $ a $ ， $ b $ ， $ c $ 是常数，等式右边是通常的加法和乘法运算．已知 $ 1*3=10 $ ， $ 1*4=12 $ ，那么 $ 1*1= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024陕西实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16426|16441", "keyPointNames": "二元一次方程组的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527900280194637824", "questionFeatureName": "阅读材料题", "questionMethodName": "整体思想", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527900280194637824", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第三次质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527900288851681280", "questionArticle": "<p>3．八年级上学期举行了数学运算竞赛，为了奖励获奖的同学，需要购买我校文创手提袋和文创水性笔作为奖品．已知1个文创手提袋和3支文创水性笔共需24元；2个文创手提袋和2支文创水性笔共需36元．</p><p>(1)求文创手提袋和文创水性笔的单价各为多少元？</p><p>(2)学校购买文创手提袋和文创水性笔两种奖品共150件，且文创手提袋的数量不少于文创水性笔的数量，应如何购买才能使总费用最少？并求出最少费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16441|16547", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527900280194637824", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527900280194637824", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第三次质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527900288029597696", "questionArticle": "<p>4．甲、乙两人同时解方程组 $ \\begin{cases} ax+y=3 \\\\ x-by=2 \\end{cases}  $ 甲看错了 $ b $ ，求得解为 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ ；乙看错了 $ a $ ，求得解为 $ \\begin{cases} x=-1 \\\\ y=3 \\end{cases}  $ ．请你求出 $ a+b $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527900280194637824", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527900280194637824", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第三次质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527900287295594496", "questionArticle": "<p>5．已知方程 $ (m-2)x{^{|m|-}}{^{1}}+(n+3)y{^{n{^{2}}}}{^{-8}}=6 $ 是关于<i>x</i>，<i>y</i>的二元一次方程．</p><p>(1)求<i>m</i>，<i>n</i>的值；</p><p>(2)当 $ x=-\\dfrac { 2 } { 3 } $ 时，求<i>y</i>的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527900280194637824", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527900280194637824", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第三次质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527900287119433728", "questionArticle": "<p>6．解方程组： $ \\begin{cases} 3\\left ( { x-1 } \\right ) =y+4 \\\\ \\dfrac { x+y } { 3 }+\\dfrac { x-y } { 6 }=1 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527900280194637824", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527900280194637824", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第三次质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527900285412352000", "questionArticle": "<p>7．已知关于 $ x $ 和 $ y $ 的方程组 $ \\begin{cases} 2x+y=1 \\\\ x+2y=k-2 \\end{cases}  $ 的解满足 $ x-y=2 $ ，则 $ k $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $　　　　B． $ 1 $　　　　C． $ 3 $　　　　D． $ 5 $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第九十中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-31", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584430146343444480", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "584430146343444480", "title": "天津市第九十中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "527900280194637824", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第三次质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527899867680645120", "questionArticle": "<p>8．解二元一次方程组：</p><p>(1) $ \\begin{cases} y=2x-3 \\\\ 2x+y=5 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=1 \\\\ 3x+2y=10 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安市曲江第一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899859296231424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899859296231424", "title": "陕西省西安市曲江第一学校2024−2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527899865298280448", "questionArticle": "<p>9．学校组织八年级学生到周原景区参观研学，如果每辆汽车坐 $ 40 $ 人，则有 $ 5 $ 人没有上车；如果每辆汽车坐 $ 48 $ 人，则空出一辆汽车，并且其中有一辆车还可以再坐 $ 11 $ 人，现假设共有 $ x $ 个学生， $ y $ 辆汽车，则可列出的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 40x+5=y \\\\ 48\\left ( { x-1 } \\right ) =y+11 \\end{cases}  $　　　　B． $ \\begin{cases} 40y+5=x \\\\ 48\\left ( { y-1 } \\right ) =x-11 \\end{cases}  $</p><p>C． $ \\begin{cases} 40y=x-5 \\\\ 48\\left ( { y-1 } \\right ) =x+11 \\end{cases}  $　　　　D． $ \\begin{cases} 40y+5=x \\\\ 48y-11=x \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安市曲江第一中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899859296231424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899859296231424", "title": "陕西省西安市曲江第一学校2024−2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527899864698494976", "questionArticle": "<p>10．如果关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x-2y=k \\\\ 3x-4y=2k-1 \\end{cases}  $ 的解 $ x $ ， $ y $ 满足 $ x-y=7 $ ，那么 $ k $ 是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．15　　　　B． $ -15 $　　　　C．14　　　　D． $ -14 $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安市曲江第一中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899859296231424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899859296231424", "title": "陕西省西安市曲江第一学校2024−2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 231, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 231, "timestamp": "2025-07-01T02:28:03.929Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}