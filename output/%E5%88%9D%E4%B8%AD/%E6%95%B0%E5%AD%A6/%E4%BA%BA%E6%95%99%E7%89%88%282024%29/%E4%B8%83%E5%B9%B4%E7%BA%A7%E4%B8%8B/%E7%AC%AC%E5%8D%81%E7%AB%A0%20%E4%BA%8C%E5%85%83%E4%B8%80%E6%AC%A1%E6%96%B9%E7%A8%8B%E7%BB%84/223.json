{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 222, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "532312515151175680", "questionArticle": "<p>1．下列各项中，属于二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x{^{2}}=3 \\\\ y=2x \\end{cases}  $ B． $ \\begin{cases} x+y=2 \\\\ 3x+y=5 \\end{cases}  $ C． $ \\begin{cases} x-y=7 \\\\ x-y{^{2}}=4 \\end{cases}  $ D． $ \\begin{cases} 2x-3y=8 \\\\ 5y-4z=6 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西抚州一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312511254667264", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312511254667264", "title": "江西省抚州市第一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "532312662727761920", "questionArticle": "<p>2．2018年“五一”期间，某大型超市两次购进同一种商品共200件，两次进价分别是25元/件和32元/件，总共投入资金5560元.</p><p>（1）超市两次购进该种商品各多少件？</p><p>（2）当超市销售该种商品160件后，出现滞销，于是将剩余商品按售价的九折全部售完，共获利2280元，已知这种商品两次的销售单价相同，求销售单价为多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西同文中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16406|16437", "keyPointNames": "销售盈亏问题|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312655622610944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312655622610944", "title": "江西省九江市同文中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532312662371246080", "questionArticle": "<p>3．某加工厂接到一批制作课桌椅的订单．已知该工厂有 $ 70 $ 名工人，每人每天平均可以加工 $ 40 $ 张课桌或 $ 60 $ 把椅子，一套课桌有 $ 1 $ 张课桌和 $ 2 $ 把椅子，为了使每天加工的课桌和椅子刚好配套，求加工课桌和椅子的工人数量．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西同文中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312655622610944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312655622610944", "title": "江西省九江市同文中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532312661901484032", "questionArticle": "<p>4．解方程组</p><p>(1) $ \\begin{cases} 2x+3y=16 \\\\ x+4y=13 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 2\\left ( { x+1 } \\right ) +3\\left ( { y-2 } \\right ) =1 \\\\ \\left ( { x+1 } \\right ) -2\\left ( { y-2 } \\right ) =4 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西同文中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16424|16425", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的特殊解法", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312655622610944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312655622610944", "title": "江西省九江市同文中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532312660794187776", "questionArticle": "<p>5．如果方程 $ 2x{^{m-1}}-3y{^{2m+n}}=1 $ 是关于<i>x</i>、<i>y</i>的二元一次方程，则 $ m+n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024江西同文中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16305|16419|16423", "keyPointNames": "代数式求值|二元一次方程的定义|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312655622610944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312655622610944", "title": "江西省九江市同文中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "532313525500289024", "questionArticle": "<p>6． $ \\begin{cases} x+3y=2-t \\\\ x-5y=3t \\end{cases}  $ 是关于 $ x $ ， $ y $ 的二元一次方程组，则 $ x+y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-01-13", "keyPointIds": "16305|16426", "keyPointNames": "代数式求值|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532313518210588672", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532313518210588672", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（一）", "paperCategory": 1}, {"id": "420314179582599168", "title": "四川省成都市锦江区成都市七中育才学校2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "532313524678205440", "questionArticle": "<p>7．若 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 是方程<i>x</i>+<i>ay</i>＝3的一个解，则<i>a</i>的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-01-13", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532313518210588672", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532313518210588672", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（一）", "paperCategory": 1}, {"id": "152064654319067136", "title": "广东省深圳市高级中学2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "532312516963115008", "questionArticle": "<p>8．某商场按定价销售某种商品时，每件可获利45元，按定价八五折销售该商品8件与定价降低35元销售该商品12件所获利润相等，该商品进价、定价分别是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|-1|360000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024江西抚州一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 3, "createTime": "2025-01-13", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312511254667264", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532312511254667264", "title": "江西省抚州市第一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "456829850646519808", "title": "北师大版课后习题 八年级上册 第五章 二元一次方程组 复习题", "paperCategory": 1}, {"id": "161070406689923072", "title": "山东省青岛市胶州市部分学校2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "533729719084163072", "questionArticle": "<p>9．某商店销售3台 $ \\mathrm{ A } $ 型和5台 $ B $ 型电脑的利润为 $ 3000 $ 元，销售5台 $ \\mathrm{ A } $ 型和3台 $ B $ 型电脑的利润为 $ 3400 $ 元．</p><p>(1)求每台 $ \\mathrm{ A } $ 型电脑和 $ B $ 型电脑的销售利润各多少元？</p><p>(2)该商店计划一次购进两种型号的电脑共 $ 50 $ 台，设购进 $ \\mathrm{ A } $ 型电脑 $ n $ 台，这 $ 50 $ 台电脑的销售总利润为<i>w</i>元．请写出<i>w</i>关于<i>n</i>的函数关系式，并判断总利润能否达到 $ 26000 $ 元，请说明理由．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆市长寿中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-13", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "533729701627469824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "533729701627469824", "title": "重庆市长寿中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "415575354553704448", "title": "重庆市大渡口区2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532701693881917440", "questionArticle": "<p>10．根据如表素材，探索完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>背景</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>深圳某学校在组织开展知识竞赛活动，去奶茶店购买A、B两种款式的奶茶作为奖品．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若买10杯A款奶茶，5杯B款奶茶，共需160元：若买15杯A型奶茶，10杯B型奶茶，共需270元．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/09/2/1/0/0/0/532701664244965382/images/img_7.png\" style=\"vertical-align:middle;\" width=\"107\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>为了满足市场的需求，奶茶店推出每杯2元的加料服务，顾客在选完款式后可以自主选择加料一份或者不加料．</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问A款奶茶和B款奶茶的销售单价各是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>在不加料的情况下，购买A、B两种款式的奶茶（两种都要），刚好花220元，请问有几种购买方案？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务3</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>根据素材2，小华恰好用了380元购买A、B两款奶茶，其中A款不加料的杯数是总杯数的 $ \\dfrac { 1 } { 3 } $ ．则其中 B型加料的奶茶买了多少杯？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-12", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 223, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 223, "timestamp": "2025-07-01T02:27:05.105Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}