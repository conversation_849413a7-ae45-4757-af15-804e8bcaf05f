{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 236, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "524198853333000192", "questionArticle": "<p>1．某水果批发市场香蕉的价格如下表</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.5pt;\"><p>购买香蕉数(千克)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 80.25pt;\"><p>不超过20千克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 145.9pt;\"><p>20千克以上但不超过40千克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 67.15pt;\"><p>40千克以上</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.5pt;\"><p>每千克的价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 80.25pt;\"><p>6元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 145.9pt;\"><p>5元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 67.15pt;\"><p>4元</p></td></tr></table><p>张强两次共购买香蕉50千克,已知第二次购买的数量多于第一次购买的数量,共付出264元,请问张强第一次,第二次分别购买香蕉多少千克?</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东滨州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-20", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524198843967119360", "questionMethodName": "分类讨论思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524198843967119360", "title": "山东省滨州市滨城区滨城区第三中学2023−2024学年八年级上学期期末数学模拟试题", "paperCategory": 1}, {"id": "129902575327944704", "title": "辽宁省葫芦岛市连山区2019届九年级毕业摸底监测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524354238811512832", "questionArticle": "<p>2．解方程组： $ \\begin{cases} 3x+2y=10 \\\\ x+4y=5 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024广西南宁二中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-20", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524354231261765632", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524354231261765632", "title": "广西壮族自治区南宁市第二中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "362360710427353088", "title": "吉林省长春市东北师大附中2023-2024学年八年级上学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524717314933039104", "questionArticle": "<p>3．根据如表素材，探索完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>背景</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>为了迎接2024年杭州茶文化“西湖悦读节”，某班级开展知识竞赛活动，去奶茶店购买<i>A</i>、<i>B</i>两种款式的奶茶作为奖品．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若买10杯<i>A</i>款奶茶，5杯<i>B</i>款奶茶，共需160元；若买15杯<i>A</i>款奶茶，10杯<i>B</i>款奶茶，共需270元．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/22/2/20/0/0/0/526012691820879873/images/img_1.png\" style='vertical-align:middle;' width=\"125\" alt=\"试题资源网 https://stzy.com\">  </p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问<i>A</i>款奶茶和<i>B</i>款奶茶的销售单价各是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>如果购买<i>A</i>、<i>B</i>两种款式的奶茶（两种都要），刚好花200元，请问购买方案分别是：<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-20", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524717306624122880", "questionFeatureName": "阅读材料题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524717306624122880", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期数学12月月考试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524717314144509952", "questionArticle": "<p>4．计算：</p><p>(1) $ {\\left( { 2\\sqrt { 5 }-3\\sqrt { 3 } } \\right) ^ {2}}-\\left ( { 4+3\\sqrt { 2 } } \\right ) \\left ( { 4-3\\sqrt { 2 } } \\right )  $ ；</p><p>(2) $ 2\\sqrt { 3 }\\div \\left ( { 3\\sqrt { 12 }-2\\sqrt { \\dfrac { 1 } { 3 } }+\\sqrt { 48 } } \\right )  $ ；</p><p>(3)解方程组 $ \\begin{cases} 4x-y=5 \\\\ 3x+2y=12 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-20", "keyPointIds": "16379|16389|16424", "keyPointNames": "二次根式的性质和化简|二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524717306624122880", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524717306624122880", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期数学12月月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524717312554868736", "questionArticle": "<p>5．从甲地到乙地有一段上坡与一段平路．如果保持上坡每小时走 $ 3\\mathrm{ k }\\mathrm{ m } $ ，平路每小时走 $ 4\\mathrm{ k }\\mathrm{ m } $ ，下坡每小时走 $ 5\\mathrm{ k }\\mathrm{ m } $ ，那么从甲地到乙地需 $ 54{\\rm  \\min } $ ，从乙地到甲地需 $ 42{ \\rm{ m } }{ \\rm{ i } }{ \\rm{ n } } $ ，设从甲地到乙地上坡与平路分别为 $ x{ \\rm{ k } }{ \\rm{ m } },y{ \\rm{ k } }{ \\rm{ m } } $ ，依题意，所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { x } { 4 }+\\dfrac { y } { 5 }=\\dfrac { 42 } { 60 } \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 4 }=\\dfrac { 54 } { 60 } \\end{cases}  $　　　　B． $ \\begin{cases} \\dfrac { x } { 4 }+\\dfrac { y } { 5 }=42 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 4 }=54 \\end{cases}  $</p><p>C． $ \\begin{cases} \\dfrac { x } { 5 }+\\dfrac { y } { 4 }=42 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 4 }=54 \\end{cases}  $　　　　D． $ \\begin{cases} \\dfrac { x } { 5 }+\\dfrac { y } { 4 }=\\dfrac { 42 } { 60 } \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 4 }=\\dfrac { 54 } { 60 } \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-20", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524717306624122880", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524717306624122880", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期数学12月月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524717311896363008", "questionArticle": "<p>6．若关于 $ x,y $ 的方程组 $ \\begin{cases} 3x-2y=2k-3 \\\\ 2x+7y=3k-2 \\end{cases}  $ 的解满足 $ x+y=2023 $ ，则 $ k $ 等于（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2022 $　　　　B．2023　　　　C．2024　　　　D．2025</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-20", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524717306624122880", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524717306624122880", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期数学12月月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524717163069874176", "questionArticle": "<p>7．3月12日是我国的植树节，这天有20位同学共植树52棵，其中男生每人植树3棵，女生每人植树2棵，若设男生有 $ x $ 人，女生有 $ y $ 人，则根据题意列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=52 \\\\ 3x+2y=20 \\end{cases}  $ B． $ \\begin{cases} x+y=52 \\\\ 2x+3y=20 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=20 \\\\ 2x+3y=52 \\end{cases}  $ D． $ \\begin{cases} x+y=20 \\\\ 3x+2y=52 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-20", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524717157722136576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "524717157722136576", "title": "辽宁省沈阳市第四十三中学2024−2025学年上学期八年级数学期末模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524715803100356608", "questionArticle": "<p>8．某地脱贫攻坚，大力发展有机农业，种植了甲、乙两种蔬菜．某超市花430元可购进甲种蔬菜15千克和乙种蔬菜20千克；花212元可购进甲种蔬菜10千克和乙种蔬菜8千克．</p><p>(1)求该超市购进甲、乙两种蔬菜的单价分别为多少元？</p><p>(2)若该超市每天购进甲、乙两种蔬菜共计100千克（甲、乙两种蔬菜重量均为整数），且花费资金不少于1160元又不多于1200元，问该超市有多少种购进方案？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖南长沙 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-20", "keyPointIds": "16438|16489|16490", "keyPointNames": "和差倍分问题|解一元一次不等式组|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524715793432485888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524715793432485888", "title": "湖南省长沙市长郡双语中学2024−2025学年上学期九年级数学第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524197489446002688", "questionArticle": "<p>9．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+3y=k \\\\ x+2y=-1 \\end{cases}  $  的解互为相反数，则<i>k</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|130000|510000|230000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河北石家庄 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 5, "createTime": "2024-12-20", "keyPointIds": "16252|16424", "keyPointNames": "相反数的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524197484106653696", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524197484106653696", "title": "河北石家庄市新华区第四十二中学2023−2024学年七年级上学期数学期末试题", "paperCategory": 1}, {"id": "491398048297295872", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年八年级上学期开学测数学试题", "paperCategory": 1}, {"id": "470590279101227008", "title": "重庆市万州区2023−2024学年七年级下学期7月期末数学试题", "paperCategory": 1}, {"id": "467873325852696576", "title": "四川省凉山彝族自治州2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "213728993287118848", "title": "浙江省台州市仙居县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524717164848259072", "questionArticle": "<p>10．（列二元一次方程组解应用题）</p><p>运动会结束后，八年级一班准备购买一批明信片奖励积极参与的同学，计划用班费180元购买<i>A</i>、<i>B</i>两种明信片共20盒，已知<i>A</i>种明信片每盒12元，<i>B</i>种明信片每盒8元，求应购买<i>A</i>、<i>B</i>两种明信片各几盒．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524717157722136576", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524717157722136576", "title": "辽宁省沈阳市第四十三中学2024−2025学年上学期八年级数学期末模拟试题", "paperCategory": 1}, {"id": "522522525844152320", "title": "辽宁省沈阳市第四十三中学 2024−2025学年上学期八年级12月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 237, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 237, "timestamp": "2025-07-01T02:28:49.540Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}