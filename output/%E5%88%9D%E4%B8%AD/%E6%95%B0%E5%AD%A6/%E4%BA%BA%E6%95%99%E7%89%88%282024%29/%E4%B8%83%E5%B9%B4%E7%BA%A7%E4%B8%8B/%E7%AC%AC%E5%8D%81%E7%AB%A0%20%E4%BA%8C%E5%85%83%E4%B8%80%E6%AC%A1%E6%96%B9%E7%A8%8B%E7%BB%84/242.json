{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 241, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "521720974300979200", "questionArticle": "<p>1．（本小题满分11分）请仔细阅读并完成相应任务：</p><p>对于未知数为 $ x $ ， $ y $ 的二元一次方程组，如果方程组的解 $ x $ ， $ y $ 满足 $ |x-y|=1 $ ，我们就说方程组的解 $ x $ 与 $ y $ 具有“邻好关系”.</p><p>任务：</p><p>（1） 方程组 $ \\begin{cases}x+2y=11,\\\\ 2x-y=2\\end{cases} $ 的解 $ x $ 与 $ y $ 是否具有“邻好关系”？说明你的理由.</p><p>（2） 若方程组 $ \\begin{cases}3x-y=5,\\\\ 2x+y=4m\\end{cases} $ 的解 $ x $ 与 $ y $ 具有“邻好关系”，求 $ m $ 的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河北邢台 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2024-12-14", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "437568717762895872", "questionFeatureName": "阅读材料题|新定义问题", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "437568717762895872", "title": "河北省邢台市第十九中学2023-2024学年七年级下学期月考数学试题", "paperCategory": 1}, {"id": null, "title": "卷2 第六章提优验收卷（B卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "521720974108041216", "questionArticle": "<p>2．（本小题满分9分）如图，A、B两地由公路和铁路相连，在这条路上有一家食品厂，它到B地的距离是到A地距离的2倍，现该食品厂从A地购买原料，全部制成食品（制作过程中有损耗）卖到B地，两次运输（第一次：A地 $ \\to  $  食品厂，第二次：食品厂 $ \\to \\mathrm{B} $ 地）共支出公路运费15 600元，铁路运费20 600元.已知公路运费为1.5元/（千米·吨），铁路运费为1元/（千米·吨）.</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/10/2/1/0/0/0/521720752950779907/images/img_4.jpg\" style=\"vertical-align:middle;\" width=\"289\" alt=\"试题资源网 https://stzy.com\"></p><p>（1） 该食品厂到A地、B地的铁路距离分别是多少千米？</p><p>（2） 该食品厂买进原料及卖出食品各多少吨？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖北武汉 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 11, "referenceNum": 2, "createTime": "2024-12-14", "keyPointIds": "16424|16438|16485|16486", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|解一元一次不等式|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "458214490573676544", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "458214490573676544", "title": "湖北省武汉市部分学校2023-2024学年七年级下学期月考数学试题", "paperCategory": 1}, {"id": null, "title": "卷2 第六章提优验收卷（B卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "521718599028875264", "questionArticle": "<p>3．用代入法解方程组 $ \\begin{cases}x=3y-1,\\\\ x-2y=4\\end{cases} $ 时，代入正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A.  $ y-2y+1=4 $ B.  $ 3y-1-2y=4 $ </p><p>C.  $ y-2(3y-1)=4 $ D.  $ 2y-1-3y=4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|130000|-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024河北石外 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 4, "createTime": "2024-12-14", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "450070466356617216", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "450070466356617216", "title": "河北省石家庄外国语教育集团2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "449185410985533440", "title": "河北省石家庄外国语学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "423869716521852928", "title": "山东省东营市东营区实验中学2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": null, "title": "卷15 期末综合检测卷（二）2025春初中上分卷 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "521719112558485504", "questionArticle": "<p>4．（本小题满分9分）今年母亲节那天，某班很多同学给妈妈准备了鲜花和礼盒，请根据图中的信息回答问题：</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/10/2/1/0/0/0/521719052730933264/images/img_17.jpg\" style=\"vertical-align:middle;\" width=\"123\" alt=\"试题资源网 https://stzy.com\"></p><p>（1） 分别求一束鲜花和一个礼盒的价格；</p><p>（2） 若小强给妈妈买了一束鲜花和一个礼盒，则小强一共花了多少钱？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 3, "createTime": "2024-12-14", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "209318748859179008", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "209318748859179008", "title": "浙教版七年级下册第2章二元一次方程组单元测试", "paperCategory": 1}, {"id": "215581793495326720", "title": "2022年七年级下册浙教版数学第2章2.4二元一次方程组的应用课时练习", "paperCategory": 1}, {"id": null, "title": "卷14 期末综合检测卷（一）2025春初中上分卷 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "521702726117400576", "questionArticle": "<p>5．定义：一次函数 $ y=kx+b $ 和 $ y=bx+k $ (其中 $ k $ 、 $ b $ 为常数， $ k\\ne 0 $ ， $ b\\ne 0 $ )互为“友好函数”．比如 $ y=3x+5 $ 和 $ y=5x+3 $ 互为“友好函数”</p><p>(1)已知点 $ P\\left ( { a,4 } \\right )  $ 在 $ y=-2x+3 $ 的“友好函数”上，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>(2) $ y=-2x+3 $ 上的点 $ \\mathbf{Q} $ 也在它的“友好函数”上，求点 $ \\mathbf{Q} $ 的坐标．</p><p>(3)若 $ y=x+b $ 和它的“友好函数”与 $ y $ 轴围成的三角形的面积是2，求 $ b $ 值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-14", "keyPointIds": "16423|16501|16535", "keyPointNames": "代入消元法解二元一次方程组|坐标与图形性质|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521702718789951488", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521702718789951488", "title": "辽宁省沈阳市第七中学2024−2025学年八年级上学期期中协作体学情调研数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "521702725089796096", "questionArticle": "<p>6．在平面直角坐标系中，若两点 $ A\\left ( { x{{}_{ 1 } },y{{}_{ 1 } } } \\right )  $ 、 $ B\\left ( { x{{}_{ 2 } },y{{}_{ 2 } } } \\right )  $ ，线段<i>AB</i>的中点是 $ C $ ，则点 $ C $ 的坐标为 $ \\left ( { \\dfrac { x{{}_{ 1 } }+x{{}_{ 2 } } } { 2 },\\dfrac { y{{}_{ 1 } }+y{{}_{ 2 } } } { 2 } } \\right )  $ ，例如：点 $ A\\left ( { 2,4 } \\right )  $ 、点 $ B\\left ( { 3,-1 } \\right )  $ ，则线段<i>AB</i>的中点 $ C $ 的坐标为 $ \\left ( { \\dfrac { 2+3 } { 2 },\\dfrac { 4+\\left ( { -1 } \\right )  } { 2 } } \\right )  $ ，即 $ C\\left ( { \\dfrac { 5 } { 2 },\\dfrac { 3 } { 2 } } \\right )  $ 请利用上面的结论解决问题：在平面直角坐标系中，若点 $ M\\left ( { a,b } \\right )  {\\rm ，\\mathit{N}} $  $ \\left ( { a+2,a+b } \\right )  $ ，线段<i>MN</i>的中点 $ P $ 恰好位于 $ y $ 轴上，且到 $ x $ 轴的距离是3，则 $ a-2b $ 的值等于<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-14", "keyPointIds": "16423|16497", "keyPointNames": "代入消元法解二元一次方程组|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521702718789951488", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521702718789951488", "title": "辽宁省沈阳市第七中学2024−2025学年八年级上学期期中协作体学情调研数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "521440912192348160", "questionArticle": "<p>7．方程组 $ \\begin{cases} 3x-2y=m \\\\ 2x+3y=1 \\end{cases}  $ 的解中 $ x $ 与 $ y $ 互为相反数，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024陕西西安市曲江第一中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-13", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521440905338855424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "521440905338855424", "title": "陕西省西安市曲江第一中学2024−2025学年上学期八年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "521442136748761088", "questionArticle": "<p>8．已知 $ m $ 为整数，关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x-2y=3m \\\\ 2x+3y=-m+4 \\end{cases}  $ 的解满足不等式组 $ \\begin{cases} x  <  3 \\\\ x+5y\\leqslant  14 \\end{cases}  $ ．</p><p>(1)解关于 $ x $ ， $ y $ 的方程组，并用 $ m $ 的代数式表示出来；</p><p>(2)求整数 $ m $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭外 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2024-12-13", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521442129098350592", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "521442129098350592", "title": "浙江省杭州市外国语学校2024—2025学年上学期八年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521442548092542976", "questionArticle": "<p>9．小林生日时，妈妈送她一个斜挎包，如图①，包的挎带由双层部分、单层部分和调节扣构成，通过调节扣加长或缩短单层部分的长度，可以使挎带的长度（单层部分与双层部分长度的和，其中调节扣所占的长度忽略不计）加长或缩短．单层部分的长度 $ x\\left ( { { \\rm{ c } }{ \\rm{ m } } } \\right )  $ 与双层部分的长度 $ y\\left ( { { \\rm{ c } }{ \\rm{ m } } } \\right )  $ 满足一次函数关系，经测量，得到如下数据：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 114.5pt;\"><p>单层部分的长度 $ x/{ \\rm{ c } }{ \\rm{ m } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>70</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>80</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>90</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 114.5pt;\"><p>双层部分的长度 $ y/{ \\rm{ c } }{ \\rm{ m } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>35</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>25</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>15</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td></tr></table><p>&nbsp;</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/09/2/1/0/0/0/521442499598000137/images/img_23.png\" style=\"vertical-align:middle;\" width=\"439\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p><p>(1)请在图②的平面直角坐标系中，描出各点，画出函数图象，并直接写出 $ y $ 关于 $ x $ 的函数表达式；</p><p>(2)当挎带的长度为 $ 110{ \\rm{ c } }{ \\rm{ m } } $ 时，求此时双层部分的长度；</p><p>(3)若刚买回来的斜挎包挎带全为双层，小林的身高最合适的挎带长度为 $ 120{ \\rm{ c } }{ \\rm{ m } } $ ，则挎带长度是否满足小林的身高要求？若满足，该如何调节挎带长度；若不满足，请说明理由．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024安徽合肥三十八中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-13", "keyPointIds": "16440|16519|16520|26247", "keyPointNames": "表格或图示问题|从函数的图象获取信息|用描点法画函数图象|待定系数法求一次函数解析式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521442540068839424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521442540068839424", "title": "安徽省合肥市第三十八中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521442548621025280", "questionArticle": "<p>10．刘阿姨承包了一些土地种植西红柿、茄子，西红柿每亩地成本2000元，茄子每亩地成本2500元（净利润 $ = $ 收入 $ - $ 成本）．“阳光农场”社团的两位同学李华和张萌帮助刘阿姨搜集到了如下信息：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/09/2/1/0/0/0/521442499602194433/images/img_26.png\" style=\"vertical-align:middle;\" width=\"296\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)种植每亩西红柿的收入为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元，每亩茄子的收入为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>(2)若刘阿姨两种蔬菜均有种植，共种植了6亩，其中西红柿种植了 $ x $ 亩，要使净利润不低于15000元，则至少种植多少亩西红柿？</p><p>(3)在（2）的条件下，设总成本为 $ w $ 元，请求出 $ w $ 与 $ x $ 之间的表达式，并计算出最小成本．（西红柿和茄子的种植亩数均为正整数）</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024安徽合肥三十八中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-13", "keyPointIds": "16438|16486|16535|16544", "keyPointNames": "和差倍分问题|一元一次不等式的应用|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521442540068839424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521442540068839424", "title": "安徽省合肥市第三十八中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}]}}, "requestData": {"pageNum": 242, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 242, "timestamp": "2025-07-01T02:29:26.068Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}