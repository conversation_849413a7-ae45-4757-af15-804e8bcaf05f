{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 207, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "537019957818728448", "questionArticle": "<p>1．1张方桌由1个桌面和4条腿组成，如果 $ { { 1 } }{ \\rm{ m } }{^{{ { 3 } }}} $ 木料可以做50个桌面或300条桌腿，现有 $ { { 5 } }{ \\rm{ m } }{^{{ { 3 } }}} $ 木料，应用多少木料做桌面、多少术料做桌腿恰好都能配成方桌？能配成多少张方桌？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西赣州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537019950772297728", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537019950772297728", "title": "江西省赣州市南康区2024−2025学年七年级上学期教学质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537734789308129280", "questionArticle": "<p>2．（算法统宗）记载的“和尚分馒头”为：“一百馒头一百僧，大僧三个更无争，小僧三人分一个，大小和尚各几丁？”，大意是：100个和尚分100个馒头，大和尚1人分3个馒头，小和尚3人分1个馒头．问大、小和尚各有多少人？设大和尚有 $ x $ 人，小和尚有 $ y $ 人，则以下列出的方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=100, \\\\ 3x+\\dfrac { y } { 3 }=100 \\end{cases}  $ B． $ \\begin{cases} x-y=100, \\\\ \\dfrac { x } { 3 }+3y=100 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=100, \\\\ 3x-\\dfrac { y } { 3 }=100 \\end{cases}  $ D． $ \\begin{cases} x-y=100, \\\\ 3x+\\dfrac { y } { 3 }=100 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537734783188639744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537734783188639744", "title": "四川省成都市八区联考2024−2025学年八年级上学期数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537732748485632000", "questionArticle": "<p>3．某校组织初二年级400名学生到威海参加拓展训练活动，已知用3辆小客车和1辆大客车每次可运送学生105人，用1辆小客车和2辆大客车每次可运送学生110人．</p><p>（1）每辆小客车和每辆大客车各能坐多少名学生？</p><p>（2）若计划租小客车m辆，大客车n辆，一次送完，且恰好每辆车都坐满：</p><p>①请你设计出所有的租车方案；</p><p>②若小客车每辆租金250元，大客车每辆租金350元，请选出最省线的租车方案，并求出最少租金．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽安庆 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16420|16435", "keyPointNames": "二元一次方程的解|分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537732740407402496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537732740407402496", "title": "安徽省安庆市外国语学校2024-2025学年九年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537732747541913600", "questionArticle": "<p>4．解方程（组）：</p><p>(1) $ \\dfrac { 2x+1 } { 3 }-\\dfrac { 5x-1 } { 6 }=-1 $ ；</p><p>(2) $ \\begin{cases} x+2y=19 \\\\ x+y=7 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽安庆 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537732740407402496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537732740407402496", "title": "安徽省安庆市外国语学校2024-2025学年九年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537733096646418432", "questionArticle": "<p>5．2024年夏天，我市举办中学生夏季球类比赛，参赛同学们积极响应，刻苦训练．为取得较好比赛成绩，某中学计划同时购进一批篮球和足球．若购进 $ 20 $ 个篮球和 $ 10 $ 个足球，共需要资金 $ 2800 $ 元；若购进 $ 3 $ 个篮球和 $ 2 $ 个足球，共需要资金 $ 460 $ 元．</p><p>(1)求篮球和足球每个的售价分别为多少元． </p><p>(2)学校计划购进篮球、足球共 $ 200 $ 个，商场售出一个篮球获利 $ 20 $ 元，一个足球的进价为 $ 50 $ 元．为了促销，商场决定每售出一个足球，返还现金 $ m $ 元，而篮球售价不变．设学校购进篮球 $ x $ 个，商场获利 $ w $ 元，请写出 $ w $ 与 $ x $ 之间的函数关系式</p><p>(3)在（2）的条件下，要使商场所有购买方案获利相同，求 $ m $ 的值． </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南驻马店 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16438|16536", "keyPointNames": "和差倍分问题|列一次函数解析式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537733089394466816", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537733089394466816", "title": "河南省驻马店市第二初级中学2024−2025学年八年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537733095564288000", "questionArticle": "<p>6．已知关于<i>x</i>、<i>y</i>的方程组 $ \\begin{cases} x+2y=k \\\\ 2x+y=k-2 \\end{cases}  $ 的解<i>x</i>，<i>y</i>的和为6，则<i>k</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>。</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南驻马店 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537733089394466816", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537733089394466816", "title": "河南省驻马店市第二初级中学2024−2025学年八年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537733096164073472", "questionArticle": "<p>7．解下列方程组</p><p>(1) $ \\begin{cases} 3x-2y=11 \\\\ 4x-5y=3 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 0.5x+0.8y=4.7 \\\\ 1.2y+0.6x=6.6 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南驻马店 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537733089394466816", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537733089394466816", "title": "河南省驻马店市第二初级中学2024−2025学年八年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537735126681165824", "questionArticle": "<p>8．综合与实践：特值法是解决数学问题的一种常用方法，即通过取题中某个未知量为特殊值，从而通过简单的运算，得出最终答案的一种方法．综合实践课上，田老师展示了如下例题：</p><p>例：已知多项式 $ 2x{^{3}}-2x{^{2}}+m $ 有一个因式是 $ x+1 $ ，求 $ m $ 的值．</p><p>解：由题意，设 $ 2x{^{3}}-2x{^{2}}+m=A\\cdot \\left ( { x+1 } \\right )  $ （<i>A</i>为整式），</p><p>由于上式为恒等式，为了方便计算，取 $ x=-1 $ ，</p><p>则 $ 2\\times {\\left( { -1 } \\right) ^ {3}}-2\\times {\\left( { -1 } \\right) ^ {2}}+m=0 $ ，解得 $ m=■ $ ．</p><p>数学思考：（1）求“ $ \\mathrm{ ■ } $ ”处 $ m $ 的值；</p><p>方法应用：（2）已知多项式 $ 2x{^{3}}-x{^{2}}-x+b $ 有一个因式是 $ 2x-1 $ ，求 $ b $ 的值；</p><p>深入探究：（3）若多项式 $ x{^{4}}+ax{^{3}}+bx-3 $ 有因式 $ \\left ( { x-1 } \\right )  $ 和 $ \\left ( { x+2 } \\right )  $ ，求 $ a $ ， $ b $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆綦江 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16353|16424", "keyPointNames": "因式分解的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537735117315284992", "questionFeatureName": "综合与实践题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "537735117315284992", "title": "重庆市綦江区2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537734168911847424", "questionArticle": "<p>9．某汽车销售公司计划购进一批新能源汽车尝试进行销售，据了解2辆 $ A $ 型新能源汽车、3辆 $ B $ 型新能源汽车的进价共计90万元；3辆 $ A $ 型新能源汽车、2辆及型新能源汽车的进价共计85万元．</p><p>(1)求 $ A $ ， $ B $ 两种型号的新能源汽车每辆的进价分别为多少万元？</p><p>(2)若该公司计划正好用220万元购进以上两种型号的新能源汽车（两种型号的新能源汽车均购买），请你通过计算帮该公司求出全部的购买方案．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16420|16434", "keyPointNames": "二元一次方程的解|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537734159936036864", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "537734159936036864", "title": "陕西省点 西安高新第一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537734168072986624", "questionArticle": "<p>10．解方程组：</p><p>(1) $ \\begin{cases} 2x-y=3 \\\\ 3\\left ( { x+2 } \\right ) +2\\left ( { y-4 } \\right ) =6 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=1 \\\\ 3x+2y=10 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537734159936036864", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "537734159936036864", "title": "陕西省点 西安高新第一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 208, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 208, "timestamp": "2025-07-01T02:25:22.426Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}