{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 202, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "543968009443909632", "questionArticle": "<p>1．已知  $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $  是关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+4y=7 $ 的解，则<i>a</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西桂林 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968003827736576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "543968003827736576", "title": "广西桂林市2024—2025学年上学期七年级数学期末试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543968008844124160", "questionArticle": "<p>2．《孙子算经》是南北朝时期重要的数学专著，包含“鸡兔同笼”等许多有趣的数学问题．如：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺．木长几何？”大意是：用一根绳量一根木，绳多出4.5尺；将绳对折再量木，绳缺少1尺．问木长多少？若设绳长为 $ x $ 尺，木长为 $ y $ 尺，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }x+1=y \\end{cases}  $ B． $ \\begin{cases} x-y=4.5 \\\\ 2x+1=y \\end{cases}  $ </p><p>C． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }x-1=y \\end{cases}  $ D． $ \\begin{cases} y-x=4.5 \\\\ 2x-1=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西桂林 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968003827736576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "543968003827736576", "title": "广西桂林市2024—2025学年上学期七年级数学期末试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317958069198848", "questionArticle": "<p>3．甲地某果蔬批发市场计划运输一批蔬菜至乙地出售，为保证果蔬新鲜需用带冷柜的货车运输．现有<i>A</i>，<i>B</i>两种型号的冷柜车，若<i>A</i>型车的平均速度为50千米/小时，<i>B</i>型车的平均速度为60千米/小时，从甲地到乙地<i>B</i>型车比<i>A</i>型车少用2小时．</p><p>(1)请求出甲乙两地相距多少千米？</p><p>(2)已知<i>A</i>型车每辆可运3吨，<i>B</i>型车每辆可运2吨，若从甲地到乙地共需运送蔬菜15吨，则两种型号货车分别需要多少辆可恰好完成运输任务？有哪几种方案？（要求：每种型号货车至少配1辆）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 27, "referenceNum": 5, "createTime": "2025-02-11", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}, {"id": "255408981006393344", "title": "河北省石家庄市裕华区石家庄外国语学校2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "173444343637778432", "title": "湘教版七年级下册第1章二元一次方程组单元测试", "paperCategory": 1}, {"id": "215766390979993600", "title": "2022年七年级下册青岛版数学第10章10.4列方程组解应用题课时练习", "paperCategory": 1}, {"id": "171932129005182976", "title": "苏科版七年级下册第10章二元一次方程组单元测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318076906414080", "questionArticle": "<p>4．某电器公司计划装运甲、乙、丙三种家电到农村销售（规定每辆汽车按规定满载，且每辆汽车只能装同一种家电），下表所示为甲、乙、丙三种家电每辆汽车满载时装运的台数及利润.</p><table style=\"border: solid 1px;border-collapse: collapse; width:414.7pt; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 153.8pt; vertical-align: top;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">丙</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 153.8pt; vertical-align: top;\"><p style=\"text-align:center;\">每辆汽车满载时装运的台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">30</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 153.8pt; vertical-align: top;\"><p style=\"text-align:center;\">利润（万元/台）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">0.05</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">0.07</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.95pt; vertical-align: top;\"><p style=\"text-align:center;\">0.04</p></td></tr></table><p>（1） 若用8辆汽车装运乙、丙两种家电共190台到A地销售（每辆汽车均满载），问装运乙、丙两种家电的汽车各多少辆？</p><p>（2） 计划用20辆汽车装运甲、乙、丙三种家电共720台到B地销售（每辆汽车均满载），问如何安排装运，可使公司获得36.6万元的利润？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-02-11", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}, {"id": null, "title": "第十章 二元一次方程组10.4 三元一次方程组的解法《2025春初中必刷题 数学七年级下册 RJ》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "543968674173984768", "questionArticle": "<p>5．下面是小权同学解二元一次方程组的过程，请认真阅读并完成相应的任务．</p><p>解方程组 $ \\begin{cases} 3x-y=4① \\\\ 6x-3y=-10② \\end{cases}  $ ，</p><p>解：由①得 $ y=3x+4 $ ③．…………第一步</p><p>将③代入②，得 $ 6x-3\\left ( { 3x+4 } \\right ) =-10 $ ，………第二步</p><p>解得 $ x=-\\dfrac { 2 } { 3 } $ ，…………第三步</p><p>将 $ x=-\\dfrac { 2 } { 3 } $ 代入①，得 $ y=2 $ ，………………第四步</p><p> $ \\therefore  $ 原方程组的解为 $ \\begin{cases} x=-\\dfrac { 2 } { 3 } \\\\ y=2 \\end{cases}  {\\rm ．} $ .………………第五步</p><p>任务：</p><p>(1)这种解二元一次方程组的方法叫作<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，以上求解步骤中，小权同学从第<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>步开始出现错误．</p><p>(2)请用加减消元法写出此题正确的解答过程．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南益阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-02-10", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968665856679936", "questionFeatureName": "阅读材料题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543968665856679936", "title": "湖南省益阳市部分学校2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}, {"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543968672391405568", "questionArticle": "<p>6．若 $ \\begin{cases} x=-2 \\\\ y=1 \\end{cases}  $ 是关于 $ x $ ， $ y $ 的二元一次方程 $ 3x-4y+2m=-8 $ 的解，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南益阳 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968665856679936", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543968665856679936", "title": "湖南省益阳市部分学校2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543968670214561792", "questionArticle": "<p>7．已知方程 $ 2x-3y=2 $ ，则 $ y $ 可用含 $ x $ 的代数式表示为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ y=\\dfrac { 2x-2 } { 3 } $　　　　B． $ y=\\dfrac { 2x+2 } { 3 } $</p><p>C． $ x=\\dfrac { 3y } { 2 }+1 $　　　　D． $ x=1-\\dfrac { 3y } { 2 } $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南益阳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-10", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968665856679936", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543968665856679936", "title": "湖南省益阳市部分学校2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "543968756239736832", "questionArticle": "<p>8．若 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的方程<i>kx﹣y</i>＝3的解，则<i>k</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西吉安 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968750690672640", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543968750690672640", "title": "江西省吉安市2024−2025学年八年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543968756696915968", "questionArticle": "<p>9．计算：</p><p>(1) $ -2023{^{0}}+\\sqrt { 8 }-{\\left( { \\sqrt { 2 } } \\right) ^ {2}} $ ；</p><p>(2)解方程组 $ \\begin{cases} 3x+2y=21 \\\\ 2x-y=7 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西吉安 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-10", "keyPointIds": "16288|16299|16323|16424", "keyPointNames": "算术平方根|实数的运算|零指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968750690672640", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543968750690672640", "title": "江西省吉安市2024−2025学年八年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542798795383283712", "questionArticle": "<p>10．（1）解方程组： $ \\begin{cases} x-2y=-8 \\\\ \\dfrac { x+3 } { 4 }+1=\\dfrac { y-1 } { 3 } \\end{cases}  $ ；</p><p>（2）解不等式组，并用数轴找解集： $ \\begin{cases} 2\\left ( { x-2 } \\right ) +3\\geqslant  x-3 \\\\ \\dfrac { 2x-1 } { 3 }  &lt;  \\dfrac { 5x+3 } { 2 } \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市第一中学校 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-09", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542798785493114880", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542798785493114880", "title": "重庆市第一中学校2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 203, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 203, "timestamp": "2025-07-01T02:24:49.141Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}