{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 218, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "534492263960846336", "questionArticle": "<p>1．为了喜迎新春，某水果店用4000元购进水果礼盒和坚果礼盒共90盒，这两种礼盒的进价、标价如下表所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">类型/价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">水果礼盒</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">坚果礼盒</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">进价（元/盒）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">60</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">标价（元/盒）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">90</p></td></tr></table><p>(1)水果礼盒和坚果礼盒各购进多少盒？</p><p>(2)为回馈客户，该水果店计划将每个水果礼盒和坚果礼盒都打<point-tag>八折</point-tag>出售，求售完这批水果礼盒和坚果礼盒水果店共盈利多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南郴州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-17", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534492250153197568", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534492250153197568", "title": "湖南省郴州市2024−2025学年七年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534493177455091712", "questionArticle": "<p>2．甲、乙两个乐团决定向某服装厂购买演出服，已知甲乐团购买的演出服每套 $ 70 $ 元，乙乐团购买的演出服每套 $ 80 $ 元，两个乐团共 $ 75 $ 人，购买演出服的总价钱为 $ 5600 $ 元．</p><p>(1)甲、乙两个乐团各有多少人？（用二元一次方程组解答）</p><p>(2)现从甲乐团抽调 $ a $ 人，从乙乐团抽调 $ b $ 人，去儿童福利院献爱心演出，并在演出后每位乐团成员向儿童们进行“心连心活动”，甲乐团每位成员负责 $ 5 $ 位小朋友，乙乐团每位成员负责 $ 6 $ 位小朋友，这样恰好使得福利院 $ 65 $ 位小朋友全部得到“心连心活动”的温暖．请写出所有的抽调方案，并说明理由．（可以仅从一个乐团抽调）</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493165220306944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534493165220306944", "title": "陕西省榆林市榆阳区2024−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534493172556144640", "questionArticle": "<p>3．对 $ x,y $ 定义一种运算，规定 $ A\\left ( { x,y } \\right ) =mx+ny $ （其中 $ m,n $ 为非零常数），如 $ A\\left ( { 2,1 } \\right ) =2m+n $ ，若 $ A\\left ( { 1,1 } \\right ) =A\\left ( { 3,-1 } \\right ) =4 $ ，则 $ A\\left ( { 2,2 } \\right )  $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493165220306944", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534493165220306944", "title": "陕西省榆林市榆阳区2024−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "534493169582383104", "questionArticle": "<p>4．甲乙两人在相距18千米的两地，若同时出发相向而行，经2小时相遇；若同向而行，且甲比乙先出发1小时，那么在乙出发后经4小时甲追上乙，求甲、乙两人的速度．设甲的速度为 $ x $ 千米/小时，乙的速度为 $ y $ 千米/小时，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+2y=18 \\\\ 5x-4y=18 \\end{cases}  $ B． $ \\begin{cases} 2x-2y=18 \\\\ 5x+4y=18 \\end{cases}  $ C． $ \\begin{cases} 2x+2y=18 \\\\ 5x=4y-18 \\end{cases}  $ D． $ \\begin{cases} 2x+2y=18 \\\\ 5x+4y=18 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493165220306944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534493165220306944", "title": "陕西省榆林市榆阳区2024−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "534493173558583296", "questionArticle": "<p>5．解方程组： $ \\begin{cases} x=y-5① \\\\ 4x+3y=29② \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493165220306944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534493165220306944", "title": "陕西省榆林市榆阳区2024−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534493604384907264", "questionArticle": "<p>6．若方程组 $ \\begin{cases} 2x-y=a \\\\ 2y-x=6 \\end{cases}  $ 中未知数<i>x</i>，<i>y</i>满足 $ x+y > 0 $ ，关于<i>x</i>的不等式组 $ \\begin{cases} 4x-a\\geqslant  0 \\\\ 3-2x > -1 \\end{cases}  $ 有且只有3个整数解，则所有满足条件的整数<i>a</i>的和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都嘉祥（JXFLS） · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16424|16485|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493595472011264", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534493595472011264", "title": "四川省成都市锦江区嘉祥外国语学校2024−2025学年上学期期末考试八年级数学模拟试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "534493423811731456", "questionArticle": "<p>7．某商店出售普通练习本和精装练习本，150本普通练习本和100精装练习本销售总额为1450元；200本普通练习本和50精装练习本销售总额为1100元．</p><p>(1)用列二元一次方程组的方法求普通练习本和精装练习本的销售单价分别是多少？</p><p>(2)该商店计划再购进普通练习本和精装练习本共500本，其中普通练习本的数量不低于375本，已知普通练习本进价为2元/个，精装练习本进价为7元/个，设购买普通练习本<i>x</i>个，获得的利润为<i>W</i>元；</p><p>①求<i>W</i>关于<i>x</i>的函数关系式；</p><p>②该商店应如何进货才能使销售总利润最大？并求出最大利润．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川成都第十二中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16438|16490|16544", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493413774761984", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534493413774761984", "title": "四川大学附属中学2024−2025学年上学期12月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534493424675758080", "questionArticle": "<p>8．关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} 4x-y=7m+1 \\\\ x-4y=3m-6 \\end{cases}  $ 的解满足 $ x-y=9 $ ，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都第十二中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493413774761984", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534493413774761984", "title": "四川大学附属中学2024−2025学年上学期12月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "534493422888984576", "questionArticle": "<p>9．（1）计算： $ \\left ( { 3-\\sqrt { 3 } } \\right ) \\left ( { \\sqrt { 3 }+3 } \\right ) -{\\left( { 3-\\sqrt { 3 } } \\right) ^ {2}}+{\\left( { \\dfrac { 2 } { 3 } } \\right) ^ {-1}} $ ；</p><p>（2）解方程组： $ \\begin{cases} \\dfrac { x } { 3 }+\\dfrac { y } { 4 }=1 \\\\ 3\\left ( { x+y } \\right ) =15 \\end{cases}  $  .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都第十二中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16389|16424", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493413774761984", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534493413774761984", "title": "四川大学附属中学2024−2025学年上学期12月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534493420770861056", "questionArticle": "<p>10．已知 $ (a-2)x{^{a{^{2}}-3}}+y=1 $ 是二元一次方程，则 $ a $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．−2</p><p>B．2</p><p>C．±2</p><p>D．无法确定</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都第十二中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534493413774761984", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534493413774761984", "title": "四川大学附属中学2024−2025学年上学期12月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 219, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 219, "timestamp": "2025-07-01T02:26:36.633Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}