{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 238, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "524718147699515392", "questionArticle": "<p>1．《九章算术》中有一道阐述“盈不足术”的问题：今有人共买物，人出八，盈三；人出七，不足四．问人数，物价几何？条件部分的译文为，现有一些人共同买一个物品，每人出8元，还盈余3元；每人出7元，则还差4元．若设共有<i>x</i>人，物品价格<i>y</i>元，则下面所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=8x-3 \\\\ y=7x+4 \\end{cases}  $</p><p>B． $ \\begin{cases} y=8x+3 \\\\ y=7x-4 \\end{cases}  $</p><p>C． $ \\begin{cases} y=7x-3 \\\\ y=8x+4 \\end{cases}  $</p><p>D． $ \\begin{cases} y=7x+3 \\\\ y=8x-4 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|370000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东济南 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 3, "createTime": "2024-12-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718141651329024", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718141651329024", "title": "山东省济南市高新区五校联考2024−2025学年八年级上学期月考数学试题", "paperCategory": 1}, {"id": "208542523127537664", "title": "重庆市梁平区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "201998236067864576", "title": "福建省福州市鼓楼区福州第十九中学2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524718388905549824", "questionArticle": "<p>2． $ \\left ( { m-3 } \\right ) x+2y{^{\\left  | { m-2 } \\right  | }}+6=0 $ 是关于 $ x $ ， $ y $ 的二元一次方程，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 2, "createTime": "2024-12-19", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718383402622976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718383402622976", "title": "陕西省西安市高新第一学校2024−2025学年上学期八年级数学第二次月考试题", "paperCategory": 1}, {"id": "160019393648304128", "title": "江苏省镇江市丹阳市第八中学2020-2021学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524718388565811200", "questionArticle": "<p>3．某生产车间共90名工人，每人每天平均能生产螺栓15个或螺帽24个，要使1个螺栓配套2个螺帽，应如何分配工人才能使每天生产的螺栓和螺帽刚好配套？设生产螺栓 $ x $ 人，生产螺帽 $ y $ 人，由题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=90 \\\\ 15x=24y \\end{cases}  $　　　　B． $ \\begin{cases} x=90-y \\\\ 2\\times 24y=15x \\end{cases}  $　　　　C． $ \\begin{cases} x+y=90 \\\\ 2\\times 15x=24y \\end{cases}  $　　　　D． $ \\begin{cases} x=90+y \\\\ \\dfrac { 15x } { 2 }=24y \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-19", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718383402622976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718383402622976", "title": "陕西省西安市高新第一学校2024−2025学年上学期八年级数学第二次月考试题", "paperCategory": 1}, {"id": "468637639840669696", "title": "新疆维吾尔自治区乌鲁木齐市第一中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524720194419204096", "questionArticle": "<p>4．为响应“把中国人的饭碗牢牢端在自己手中”的号召，确保粮食安全，优选品种，提高产量，某农业科技小组对<i>A</i>、<i>B</i>两个玉米品种进行实验种植对比研究．去年<i>A</i>、<i>B</i>两个品种各种植了10亩．收获后<i>A</i>、<i>B</i>两个品种的售价均为2.4元/<i>kg</i>，且<i>B</i>品种的平均亩产量比<i>A</i>品种高100千克，<i>A</i>、<i>B</i>两个品种全部售出后总收入为21600元．</p><p>（1）求<i>A</i>、<i>B</i>两个品种去年平均亩产量分别是多少千克？</p><p>（2）今年，科技小组优化了玉米的种植方法，在保持去年种植面积不变的情况下，预计<i>A</i>、<i>B</i>两个品种平均亩产量将在去年的基础上分别增加<i>a</i>%和2<i>a</i>%．由于<i>B</i>品种深受市场欢迎，预计每千克售价将在去年的基础上上涨<i>a</i>%，而<i>A</i>品种的售价保持不变，<i>A</i>、<i>B</i>两个品种全部售出后总收入将增加 $ \\dfrac { 20 } { 9 }a\\% $ ，求<i>a</i>的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|510000|340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2020重庆 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 12, "referenceNum": 5, "createTime": "2024-12-19", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207160336322961408", "questionFeatureName": "生活背景问题", "questionMethodName": "函数与方程思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "207160336322961408", "title": "重庆市2020年中考数学试题A卷", "paperCategory": 1}, {"id": "207161250756730880", "title": "重庆市2020年中考数学试题（B卷）", "paperCategory": 1}, {"id": "524720185384673280", "title": "重庆市万州区2024—2025学年九年级上学期数学期末模拟（1）", "paperCategory": 1}, {"id": "128531965121175552", "title": "安徽省2021年中考一模数学试题", "paperCategory": 1}, {"id": "129172366421172224", "title": "四川师范大学实验外国语学校2021年中考二诊数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524619473854177280", "questionArticle": "<p>5． $ ．2024 $ 年成都世园会吉祥物为“桐妹儿”，核心创意来自中国特有的孑遗植物珙桐 $ ( $ 又称鸽子花 $ ) $ 和三星堆“青铜神鸟”，寓意和平友好、包容互鉴，富有深刻的文化内涵和巴蜀特色 $ . $ 某商店购进“桐妹儿”的玩偶和钥匙扣，进货价分别为每个 $ 66 $ 元和 $ 59 $ 元，准备以每个 $ 88 $ 元和 $ 79 $ 元进行销售．</p><p> $ (1) $ 该店铺购进玩偶和钥匙扣共 $ 80 $ 个，若进货后能全部售出，则可获利 $ 1702 $ 元，问分别购进玩偶和钥匙扣多少个？</p><p> $ (2) $ 该店铺打算把钥匙扣调价销售，若按原价销售，平均每天可售 $ 8 $ 个，经调查发现，每降价 $ 1 $ 元，平均每天可多售 $ 2 $ 个，将销售价定为每个多少元时，能使钥匙扣平均每天销售利润为 $ 288 $ 元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川四川师大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-18", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524619465138413568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "524619465138413568", "title": "四川师范大学附属中学教育集团2024−2025学年九年级上学期期中数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "523603781075902464", "questionArticle": "<p>6．网约车已成为人们出行的首选便捷工具，某网约车行车计费规则如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>项目</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 62.2pt;\"><p>时长费</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 62.2pt;\"><p>里程费</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 62.2pt;\"><p>远途费</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>单价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 62.2pt;\"><p>0.5元/分钟</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 62.2pt;\"><p>1.6元/千米</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 62.2pt;\"><p>0.4元/千米</p></td></tr></table><p>乘客车费由时长费、里程费、远途费三部分构成，其中时长费按行车实际时间计算；里程费按行车的实际里程计算；远途费收取标准如下：行车里程10千米以内（含10千米），不收远途费，超过10千米的，超出部分每千米收0.4元．</p><p>(1)张老师乘坐该网约车，行车里程为20千米，行车时间为30分钟，需付车费<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>(2)若小明乘坐该网约车，行车里程为<i>a</i>千米，行车时间为<i>b</i>分钟．请用含<i>a</i>、<i>b</i>的代数式表示车费，并化简：当 $ a\\leqslant  10 $ 时，小明应付车费<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；当 $ &nbsp;a &gt; 10 $ 时，小明应付车费<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>(3)小明和张老师都乘坐该网约车，行车里程分别是7.5千米和12千米，如果两人所付车费相同，那么两人所乘的两辆网约车的行车时间相差<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>分钟．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南郑州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-18", "keyPointIds": "16304|16430", "keyPointNames": "列代数式|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "523603773714898944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "523603773714898944", "title": "河南省郑州市九校联考2024−2025学年 上学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "522160278957498368", "questionArticle": "<p>7．某美食店的 $ A $ ， $ B $ 两种菜品，每份成本均为14元，售价分别为20元、18元时，这两种菜品每天的营业额共为1120元，总利润为280元．</p><p>(1)该店每天卖出这两种菜品共多少份？</p><p>(2)该店为了增加利润，准备降低 $ A $ 种菜品的售价，同时提高 $ B $ 种菜品的售价，售卖时发现， $ A $ 种菜品售价每降0.5元可多卖1份； $ B $ 种菜品售价每提高0.5元就少卖1份，如果 $ A $ 种菜品每份售价降价的钱数和 $ B $ 种菜品每份涨价的钱数一样多，那么当 $ A $ 种菜品降价多少元时，两种菜品的利润总和为300元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-18", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522160270208180224", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "522160270208180224", "title": "辽宁省沈阳市南昌初级中学2024−2025学年九年级上学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "523603499000569856", "questionArticle": "<p>8．“文明其精神，野蛮其体魄”，为进一步提升学生体质健康水平，我市某校计划用 $ 640 $ 元购买 $ 12 $ 个体育用品，备选体育用品及单价如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">备用体育用品</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">足球</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">篮球</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">排球</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">单价（元）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ 80 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ 60 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ 40 $ </p></td></tr></table><p>（1）若 $ 640 $ 元全部用来购买足球和排球共 $ 12 $ 个，求足球和排球各买多少个？</p><p>（2）若学校先用一部分资金购买了 $ m $ 个排球，再用剩下的资金购买了相同数量的足球和篮球，此时正好剩余 $ 40 $ 元，求 $ m $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|620000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河北邯郸 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 11, "referenceNum": 3, "createTime": "2024-12-18", "keyPointIds": "16416|16426", "keyPointNames": "其他问题|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "523603490872008704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "523603490872008704", "title": "河北省邯郸市多校2024−2025学年上学期第三次月考七年级数学试题", "paperCategory": 1}, {"id": "173844593011630080", "title": "甘肃省定西市2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "147466847188721664", "title": "河北省邢台市2021-2022学年七年级上学期第三次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "522160167166713856", "questionArticle": "<p>9．用适当方法解方程组：</p><p>(1) $ \\begin{cases} y=6-3x \\\\ 7x-2y=1 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 4x-3y=14 \\\\ 5x+3y=31 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2024-12-17", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519617830829989888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519617830829989888", "title": "辽宁省沈阳市第一二六中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}, {"id": "522160159528886272", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "522160165547712512", "questionArticle": "<p>10．已知 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} x=2 \\\\ y=1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ 是二元一次方程组 $ \\begin{cases} mx+ny=8 \\\\ nx-my=1 \\end{cases}  $ 的解，则 $ 2m-n $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B．4C．3D．8</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024辽宁沈阳 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2024-12-17", "keyPointIds": "16305|16420|16423", "keyPointNames": "代数式求值|二元一次方程的解|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519617830829989888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519617830829989888", "title": "辽宁省沈阳市第一二六中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}, {"id": "522160159528886272", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 239, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 239, "timestamp": "2025-07-01T02:29:03.946Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}