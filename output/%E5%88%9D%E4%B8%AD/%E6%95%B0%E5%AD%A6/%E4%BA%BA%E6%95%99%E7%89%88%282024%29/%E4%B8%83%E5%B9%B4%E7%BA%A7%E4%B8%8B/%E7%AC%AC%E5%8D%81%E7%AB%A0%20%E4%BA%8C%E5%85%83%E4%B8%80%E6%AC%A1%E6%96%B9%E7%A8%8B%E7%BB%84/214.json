{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 213, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "536617057598611456", "questionArticle": "<p>1．【问题情境】</p><p>小明所在的班级准备开展知识竞赛，需要购买<i>A</i>，<i>B</i>两种款式的运动盲盒作为奖品．</p><p>素材 $ 1 $ ：已知甲、乙两个商店均有价格、款式相同的两种运动盲盒出售，在无促销活动时，若买 $ 15 $ 个<i>A</i>款运动盲盒、 $ 10 $ 个<i>B</i>款运动盲盒，共需 $ 230 $ 元；若买 $ 25 $ 个<i>A</i>款运动盲盒、 $ 25 $ 个<i>B</i>款运动盲盒，共需 $ 450 $ 元．</p><p>素材2：现甲、乙两商店开展不同的促销活动：</p><p>甲商店：用 $ 35 $ 元购买会员卡成为会员后，凭会员卡购买商店内任何商品，一律按商品价格的 $ 8 $ 折出售 $ ( $ 已知小明在此之前不是该商店的会员 $ ) $ ；乙商店：购买商店内任何商品，一律按商品价格的 $ 9 $ 折出售．</p><p>【解决问题】</p><p>(1)在无促销活动时，求<i>A</i>款运动盲盒和<i>B</i>款运动盲盒的销售单价各是多少元？</p><p>(2)小明计划在促销期间购买<i>A</i>，<i>B</i>两款运动盲盒共40个，其中<i>A</i>款运动盲盒<i>m</i>个（ $ 0  &lt;  m  &lt;  40 $ ），若小明在甲商店成为会员购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；若在乙商店购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．（均用含<i>m</i>的代数式表示）</p><p>(3)请你帮小明算一算，在（2）的条件下，购买<i>A</i>款运动盲盒的数量<i>m</i>在什么范围内时，去甲商店更合算？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳实验学校 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-23", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617049658793984", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536617049658793984", "title": "广东省深圳实验学校初中部2024−2025学年上学期八年级期末数学试卷", "paperCategory": 1}, {"id": "533727854799921152", "title": "湖南省株洲市荷塘区景弘中学2024−2025学年九年级上学期第一次月考数学试卷（9月份）", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537437856576348160", "questionArticle": "<p>2．一个两位数的十位数字与个位数字的和为8，若把这个两位数加上18，正好等于将这个两位数的十位数字与个位数字对调后所组成的新两位数，则原来的两位数为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|510000|640000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆南岸 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 6, "referenceNum": 3, "createTime": "2025-01-23", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537437845406916608", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "537437845406916608", "title": "重庆市南岸区2024—2025学年八年级上学期期末质量监测数学试卷", "paperCategory": 1}, {"id": "257521689579266048", "title": "四川省成都市蒲江县蒲江中学2022-2023学年八年级上学期期中数学试题", "paperCategory": 1}, {"id": "151320661440372736", "title": "宁夏中宁县第三中学2021-2022学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537356475703795712", "questionArticle": "<p>3．已知<i>a</i>,<i>b</i>,<i>c</i>分别是等腰△<i>ABC</i>三边的长,且满足<i>ac</i>=12-<i>bc</i>,若<i>a</i>,<i>b</i>,<i>c</i>均为正整数,则这样的等腰△<i>ABC</i>有&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A.3个B.4个C.5个D.6个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-22", "keyPointIds": "16420|16640|16661", "keyPointNames": "二元一次方程的解|三角形三边关系|等腰三角形的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537356474646831104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537356474646831104", "title": "第16讲　简单的轴对称图形(上)（练习）BS", "paperCategory": 10}, {"id": "242302165263360000", "title": "湖南省长沙市长郡教育集团2022-2023学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537321106358706176", "questionArticle": "<p>4．合肥市某中学学生张强到某服装商场进行社会调查,了解到该商场为了激励营业员的工作积极性,实行“月总收入=基本工资+计件奖金”的方法(即营业员月总收入由基本工资和计件奖金两部分构成),并获得如下信息:</p><p>营业员A:月销售件数200件,月总收入4 500元;</p><p>营业员B:月销售件数300件,月总收入5 000元．</p><p>假设营业员的月基本工资为<i>x</i>元,销售每件服装奖励<i>y</i>元．</p><p>(1)求<i>x</i>,<i>y</i>的值．</p><p>(2)顾客如果购买甲服装3件,乙服装2件,丙服装1件共需1 500元;如果购买甲服装1件,乙服装2件,丙服装3件共需1 620元．某顾客想购买甲、乙、丙服装各一件,共需多少元?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 8, "createTime": "2025-01-22", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519570190486511616", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519570190486511616", "title": "第13讲　三元一次方程组的解法（练习）", "paperCategory": 10}, {"id": "542766913908154368", "title": "第15讲　三元一次方程组的解法（练习）", "paperCategory": 10}, {"id": "552437888945790976", "title": "XJ第22讲　三元一次方程组的解法（练习）", "paperCategory": 10}, {"id": "552902745092562944", "title": "第8讲 ∗三元一次方程组的解法（练习）", "paperCategory": 10}, {"id": "537321103460442112", "title": "第5讲　三元一次方程组的解法（练习）LJ七下", "paperCategory": 10}, {"id": "534033566432272384", "title": "第8讲　三元一次方程组的解法（练习）", "paperCategory": 10}, {"id": "523817890342019072", "title": "第4讲　三元一次方程组的解法（练习）", "paperCategory": 10}, {"id": null, "title": "第3章 一次方程（组） $ {\\rm \\mathbf{^{*}}} $ 3.8 三元一次方程组《2024秋初中必刷题 数学七年级上册 XJ》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "537320778087309312", "questionArticle": "<p>5．如图,在大长方形<i>ABCD</i>中,放入六个完全一样的小长方形,所标尺寸如图所示,请你利用方程组的思想方法求出图中阴影部分的面积．</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/22/2/1/0/0/0/537320756616667136/images/img_1.png\" style=\"vertical-align:middle;\" width=\"162\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|460000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 8, "createTime": "2025-01-22", "keyPointIds": "16424|16439", "keyPointNames": "加减消元法解二元一次方程组|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519569341345472512", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519569341345472512", "title": "第12讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "552437495310360576", "title": "XJ第21讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "552902630214770688", "title": "第7讲 实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "537320776438947840", "title": "第3讲　实际问题与二元一次方程组（练习）LJ七下", "paperCategory": 10}, {"id": "534032591801851904", "title": "第7讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "445716900120666112", "title": "福建省福州第一中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "523817651086336000", "title": "第3讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "220270269365002240", "title": "海南省省直辖县级行政单位陵水黎族自治县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537320777164562432", "questionArticle": "<p>6．一个两位数,个位数字比十位数字大2,若把个位数字和十位数字对调,则所得的新的两位数比原数的2倍少17．若设原数的个位数字为<i>x</i>,十位数字为<i>y</i>,则下列方程组正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x-y=2,\\\\ yx=2xy-17\\end{cases} $ B． $ \\begin{cases}x+y=2,\\\\ xy=2yx-17\\end{cases} $ </p><p>C． $ \\begin{cases}x=y+2,\\\\ 10x+y=2(10y+x)-17\\end{cases} $ D． $ \\begin{cases}x+y=2,\\\\ 10y+x=2(10x+y)-17\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 9, "createTime": "2025-01-22", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519569341345472512", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519569341345472512", "title": "第12讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "552437495310360576", "title": "XJ第21讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "552902630214770688", "title": "第7讲 实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "552902865309704192", "title": "第9讲 实践与探索（练习）", "paperCategory": 10}, {"id": "542766181343600640", "title": "第13讲　二元一次方程组的应用（练习）", "paperCategory": 10}, {"id": "537320776438947840", "title": "第3讲　实际问题与二元一次方程组（练习）LJ七下", "paperCategory": 10}, {"id": "534032591801851904", "title": "第7讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "523817651086336000", "title": "第3讲　实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "170287431018651648", "title": "2022年八年级上册北师版数学第五章5应用二元一次方程组——里程碑上的数课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537320689633632256", "questionArticle": "<p>7．对于<i>x</i>,<i>y</i>我们定义一种新运算“※”:<i>x</i>※<i>y</i>=<i>ax</i>+<i>by</i>,其中<i>a</i>,<i>b</i>为常数,等式的右边是通常的加法和乘法运算．已知:5※2=7,3※(−4)=12,则4※3=<u>　　　　</u>．<i>&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 5, "createTime": "2025-01-22", "keyPointIds": "16278|16424", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519568235718549504", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519568235718549504", "title": "第11讲　消元——解二元一次方程组（练习）", "paperCategory": 10}, {"id": "537320686869585920", "title": "第2讲　消元——解二元一次方程组（练习）LJ七下", "paperCategory": 10}, {"id": "534031429346304000", "title": "第6讲　消元——解二元一次方程组（练习）", "paperCategory": 10}, {"id": "446418425918824448", "title": "四川省德阳市德阳外国语学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "523817535180939264", "title": "第2讲　消元——解二元一次方程组（练习）", "paperCategory": 10}], "questionTypeCode": "5"}, {"questionId": "537320563397664768", "questionArticle": "<p>8．如果二元一次方程组 $ \\begin{cases}x+y=☆,\\\\ 2x+y=16\\end{cases} $ 的解为 $ \\begin{cases}x=6,\\\\ y=△,\\end{cases} $ 那么“☆”表示的数为<u>　　　　</u>．<i>&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 5, "createTime": "2025-01-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519536082712567808", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519536082712567808", "title": "第10讲　二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "537320561409564672", "title": "第1讲　二元一次方程组的概念（练习）LJ七下", "paperCategory": 10}, {"id": "534030111047196672", "title": "第5讲　二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "523817379203162112", "title": "第1讲　二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "196959291999297536", "title": "北京师范大学附属实验中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "536333988622802944", "questionArticle": "<p>9．阅读下列解题过程，将空格补充完整，借鉴其中一种方法解答后面给出的试题：</p><p>问题：某人买 $ 13 $ 个鸡蛋， $ 5 $ 个鸭蛋、 $ 9 $ 个鹅蛋共用去了 $ 9.25 $ 元；买 $ 2 $ 个鸡蛋， $ 4 $ 个鸭蛋、 $ 3 $ 个鹅蛋共用去了 $ 3.20 $ 元．试问只买鸡蛋、鸭蛋、鹅蛋各一个共需多少元．</p><p>分析：设买鸡蛋，鸭蛋、鹅蛋各一个分别需 $ x $ 、 $ y $ 、 $ z $ 元，则需要求 $ x+y+z $ 的值．由题意，知 $ \\begin{cases} 13x+5y+9z=9.25① \\\\ 2x+4y+3z=3.20② \\end{cases}  $ ；</p><p>视 $ x $ 为常数，将上述方程组看成是关于<i>y</i>、<i>z</i>的二元一次方程组，化“三元”为“二元”、化“二元”为“一元”从而获解．</p><p>解法1：视<i>x</i>为常数，依题意得 $ \\begin{cases} 5y+9z=9.25-13x③ \\\\ 4y+3z=3.20-2x④ \\end{cases}  $ </p><p>解这个关于<i>y</i>、<i>z</i>的二元一次方程组得 $ \\begin{cases} y=0.05+x \\\\ z=1-2x \\end{cases}  $ </p><p>于是 $ x+y+z= $  _．</p><p>评注：也可以视<i>z</i>为常数，将上述方程组看成是关于 $ x $ 、 $ y $ 的二元一次方程组，解答方法同上，你不妨试试．</p><p>分析：视 $ x+y+z $ 为整体，由（1）（2）恒等变形得</p><p> $ 5\\left ( { x+y+z } \\right ) +4\\left ( { 2x+z } \\right ) =9.25 $ ，</p><p> $ 4\\left ( { x+y+z } \\right ) -\\left ( { 2x+z } \\right ) =3.20 $ ．</p><p>解法 $ 2 $ ：设 $ x+y+z=a $ ， $ 2x+z=b $ ，代入（1）、（2）可以得到如下关于 $ a $ 、 $ b $ 的二元一次方程组_，解得 _</p><p>评注：运用整体的思想方法指导解题．视 $ x+y+z $ ， $ 2x+z $ 为整体，令 $ a=x+y+z $ ， $ b=2x+z $ ，代入 $ ① $ 、 $ ② $ 将原方程组转化为关于 $ a $ 、 $ b $ 的二元一次方程组从而获解．</p><p>请你运用以上介绍的任意一种方法解答如下数学竞赛试题：</p><p>购买五种教学用具 $ A{{}_{ 1 } } $ 、 $ A{{}_{ 2 } } $ 、 $ A{{}_{ 3 } } $ 、 $ A{{}_{ 4 } } $ 、 $ A{{}_{ 5 } } $ 的件数和用钱总数列成下表：那么，购买每种教学用具各一件共需多少元？</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 124pt;\"><p style=\"text-indent:70pt;\">品名次数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ A{{}_{ 1 } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ A{{}_{ 2 } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ A{{}_{ 3 } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.2pt;\"><p> $ A{{}_{ 4 } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ A{{}_{ 5 } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>总钱数</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 124pt;\"><p>第一次购买件数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ l $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ 3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ 4 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.2pt;\"><p> $ 5 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ 6 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p> $ 1992 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 124pt;\"><p>第二次购买件数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ l $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ 5 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ 7 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.2pt;\"><p> $ 9 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ 11 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p> $ 2984 $ </p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东青岛市青岛大学附属中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-22", "keyPointIds": "16424|16444", "keyPointNames": "加减消元法解二元一次方程组|三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536333980448104448", "questionFeatureName": "阅读材料题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536333980448104448", "title": "山东省青岛大学附属中学2024−2025学年八年级上学期期末数学模拟检测题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536333770154090496", "questionArticle": "<p>10．“国美”、“苏宁”两家电器商场出售同样的空气净化器和过滤网，空气净化器和过滤网在两家商场的售价一样．已知买1个空气净化器和2个过滤网要花费2440元，买2个空气净化器和3个过滤网要花费4760元．</p><p>(1)求1个空气净化器与1个过滤网的销售价格分别是多少元？</p><p>(2)为了迎接新年，两家商场都在搞促销活动，“国美”规定：这两种商品都打九五折；“苏宁”规定：买1个空气净化器赠送2个过滤网．若某单位想要买10个空气净化器和30个过滤网，如果只能在一家商场购买，请问选择哪家商场购买更合算，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西萍乡 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-22", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536333762298159104", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "536333762298159104", "title": "江西省萍乡市2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 214, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 214, "timestamp": "2025-07-01T02:26:01.831Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}