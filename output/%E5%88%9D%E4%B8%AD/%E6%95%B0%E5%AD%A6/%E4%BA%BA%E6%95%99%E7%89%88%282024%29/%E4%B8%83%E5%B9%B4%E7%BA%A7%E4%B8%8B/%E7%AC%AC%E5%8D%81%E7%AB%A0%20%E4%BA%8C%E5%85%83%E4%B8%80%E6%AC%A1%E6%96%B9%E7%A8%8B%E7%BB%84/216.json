{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 215, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "535214549923831808", "questionArticle": "<p>1．随着“低碳生活，绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具，某汽车销售公司计划购进一批新能源汽车进行销售，据了解，3辆 $ \\mathrm{ A } $ 型汽车，2辆 $ B $ 型汽车的进价共计95万元；2辆 $ \\mathrm{ A } $ 型汽车，3辆 $ B $  型汽车的进价共计80万元．求 $ \\mathrm{ A } $ ， $ B $ 两种型号的汽车每辆进价分别为多少万元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖北十堰 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "535214541342285824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "535214541342285824", "title": "2024年湖北省十堰市实验中学教联体中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "535214548795564032", "questionArticle": "<p>2．我国古代数学名著《九章算术》中记载：“今有黄金九枚，白银十一枚，称之重适等．交易其一，金轻十三两．问金、银一枚各重几何？”译文为：现有一袋黄金9枚，一袋白银11枚，这两袋的重量恰好相等．若两袋中交换1枚黄金和1枚白银，则原来装黄金的袋子比原来装白银的袋子轻13两，问黄金和白银1枚各重几两．</p><p>答∶1枚黄金重<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>两；1枚白银重<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>两．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024湖北十堰 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "535214541342285824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "535214541342285824", "title": "2024年湖北省十堰市实验中学教联体中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "535214220402532352", "questionArticle": "<p>3．甲、乙两人都解方程组 $ \\begin{cases} ax+y=2 \\\\ 2x-by=1 \\end{cases}  $ ，甲看错<i>a</i>解得 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ ，乙看错<i>b</i>解得 $ \\begin{cases} x=1 \\\\ y=1 \\end{cases}  $ ，则方程组正确的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南周口 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "535214214056550400", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "535214214056550400", "title": "2024年河南省周口市郸城县三校联考一模数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "534868713587122176", "questionArticle": "<p>4．舒兰大米种植区域处于北纬43度世界黄金水稻带．舒兰大米具有营养丰富、绵软柔糯等特点．某校食堂计划采购甲、乙两种舒兰大米，若购进甲种大米500千克和乙种大米300千克需花费11000元；若购进甲种大米200千克和乙种大米600千克需花费9200元．求每千克甲种大米和每千克乙种大米的价格．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024吉林吉林 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534868705496309760", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534868705496309760", "title": "2024年吉林省吉林市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536618461121781760", "questionArticle": "<p>5．“巴山大峡谷”位于四川省达州市宣汉县，这里山势奇特，河水清澈，溶洞成群，动物多而珍贵，植物丰富而罕见，是个旅游的好地方．若购买9张大象洞门票和4张桃溪谷门票共花900元，购买3张大象洞门票和2张桃溪谷门票共花360元．</p><p>(1)大象洞门票，桃溪谷门票每张各多少元？</p><p>(2)若某旅游公司共有游客50人，设购买大象洞门票 $ a $ 张，且购买大象洞门票不超过20张，设该旅游公司门票总费用为 $ w $ 元，请写出 $ w $ 与 $ a $ 的函数关系式，并求出门票总费用最低为多少钱？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川达州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16441|16547", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536618446378803200", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "536618446378803200", "title": "四川省达州市2024−2025学年八年级上学期1月期末数学测试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536618458781360128", "questionArticle": "<p>6．（1）计算： $ \\left  | { 2-\\sqrt { 3 } } \\right  | +\\sqrt { 2 }\\times \\left ( { \\sqrt { 8 }-\\sqrt { 6 } } \\right ) -{\\left( { \\dfrac { 1 } { 2 } } \\right) ^ {-2}} $  ；   </p><p>（2）解方程组： $ \\begin{cases} x-3y=1 \\\\ 2x+y=9 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川达州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-21", "keyPointIds": "16372|16379|16381|16424", "keyPointNames": "负整数指数幂|二次根式的性质和化简|二次根式的乘法|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536618446378803200", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "536618446378803200", "title": "四川省达州市2024−2025学年八年级上学期1月期末数学测试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536618452875780096", "questionArticle": "<p>7．下列方程：① $ x+y=1 $ ；② $ 2x-\\dfrac { 2 } { y }=1 $ ；③ $ x{^{2}}+2x=-1 $ ；④ $ 5xy=1 $ ；⑤ $ x-\\dfrac { 1 } { 3 }y=2 $ ，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①⑤　　　　B．①②　　　　C．①④　　　　D．①②④</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025江苏无锡市天一实验中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 8, "referenceNum": 2, "createTime": "2025-01-21", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431136908029952", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "584431136908029952", "title": "江苏省无锡市天一实验学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "536618446378803200", "title": "四川省达州市2024−2025学年八年级上学期1月期末数学测试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "536334070550142976", "questionArticle": "<p>8．港珠澳大桥是世界上最长的跨海大桥，它由桥梁和隧道两部分组成，桥梁和隧道全长共 $ 55{ \\rm{ k } }{ \\rm{ m } } $ ．其中桥梁长度比隧道长度的9倍少 $ 4{ \\rm{ k } }{ \\rm{ m } } $ ．求港珠澳大桥的桥梁长度和隧道长度．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|610000|460000|220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2021吉林 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 4, "createTime": "2025-01-21", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "202121325367828480", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "202121325367828480", "title": "吉林省2021年中考数学真题试卷", "paperCategory": 1}, {"id": "536334062773903360", "title": "陕西省宝鸡市宝鸡一中2024−2025学年上学期八年级数学期末学情调研测试题", "paperCategory": 1}, {"id": "170287431018651648", "title": "2022年八年级上册北师版数学第五章5应用二元一次方程组——里程碑上的数课时练习", "paperCategory": 1}, {"id": "189352463006212096", "title": "海南省琼海市2022年中考模拟考试（一）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536334069556092928", "questionArticle": "<p>9．解方程组 $ \\begin{cases} \\dfrac { x-1 } { 6 }-\\dfrac { 2-y } { 3 }=1 \\\\ 2x+y=13 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024陕西宝鸡 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-01-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536334062773903360", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "536334062773903360", "title": "陕西省宝鸡市宝鸡一中2024−2025学年上学期八年级数学期末学情调研测试题", "paperCategory": 1}, {"id": "470589811507634176", "title": "山东省滨州市滨城区高新八校2023−2024学年七年级下学期7月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536333545393922048", "questionArticle": "<p>10．“脐橙结硕果，香飘引客来”，赣南脐橙以其“外表光洁美观，肉质脆嫩，风味浓甜芳香”的特点饮誉中外．现欲将一批脐橙运往外地销售，若用2辆<i>A</i>型车和1辆<i>B</i>型车载满脐橙一次可运走 $ 10{\\rm t} $ ；用1辆<i>A</i>型车和2辆<i>B</i>型车载满脐橙一次可运走 $ 11{ \\rm{ t } } $ ，现有脐橙 $ 31{ \\rm{ t } } $ ，计划同时租用 <i>A</i> 型车<i>a</i> 辆，<i>B</i> 型车<i>b</i>辆，一次运完，且恰好每辆车都载满脐橙．</p><p>根据以上信息，解答下列问题：</p><p>(1)1 辆<i>A </i>型车和1辆<i>B </i>型车都载满脐橙一次可分别运送多少吨?</p><p>(2)请你帮该物流公司设计租车方案；</p><p>(3)若1辆<i>A </i>型车需租金100元/次，1辆<i>B</i>型车需租金120元/次．请选出费用最少的租车方案，并求出最少租车费．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南开封 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-01-21", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536333537466687488", "questionFeatureName": "生活背景问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536333537466687488", "title": "河南省开封市祥符区2024-2025学年八年级上学期期末调研考试数学试题", "paperCategory": 1}, {"id": "160040403562962944", "title": "广东省佛山市南海区第一中学2021-2022学年八年级上学期期中数学试题", "paperCategory": 1}, {"id": "158923972460978176", "title": "广东省佛山市南海区第一中学2021-2022学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 216, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 216, "timestamp": "2025-07-01T02:26:16.130Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}