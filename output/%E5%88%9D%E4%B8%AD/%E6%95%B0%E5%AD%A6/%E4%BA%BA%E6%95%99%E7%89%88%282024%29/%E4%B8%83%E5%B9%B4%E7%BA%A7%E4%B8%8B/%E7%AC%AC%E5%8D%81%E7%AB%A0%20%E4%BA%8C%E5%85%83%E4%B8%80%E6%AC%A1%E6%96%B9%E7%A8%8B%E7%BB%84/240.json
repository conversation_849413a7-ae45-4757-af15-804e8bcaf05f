{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 239, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "522160165111504896", "questionArticle": "<p>1．某营养师用甲、乙两种原料配置营养品．每克甲原料含0.5单位蛋白质和1单位铁质，每克乙原料含0.7单位蛋白质和0.4单位铁质．如果每份营养品需要35单位蛋白质和40单位铁质，那么每份营养品中甲、乙原料各多少克恰好满足需求？设每份营养品需要甲原料<i>x</i>克，乙原料<i>y</i>克，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 0.5x+y=35 \\\\ 0.7x+0.4y=40 \\end{cases}  $ B． $ \\begin{cases} 0.5x+0.7y=35 \\\\ x+0.4y=40 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+0.4y=35 \\\\ 0.5x+0.7y=40 \\end{cases}  $ D． $ \\begin{cases} 0.7x+0.4y=35 \\\\ 0.5x+y=40 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024辽宁沈阳 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-17", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519617830829989888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519617830829989888", "title": "辽宁省沈阳市第一二六中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}, {"id": "522160159528886272", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "522158609792278528", "questionArticle": "<p>2．上数学课时，陈老师让同学们解一道关于<i>x</i>、<i>y</i>的方程组 $ \\begin{cases} ax+3y=-5① \\\\ 2x-by=14② \\end{cases}  $ ，并请小方和小龙两位同学到黑板上板演．可是小方同学看错了方程①中的<i>a</i>，得到方程组的解为 $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ ，小龙同学看错了方程②中的<i>b</i>，得到方程组的解为 $ \\begin{cases} x=-2 \\\\ y=-1 \\end{cases}  $ ，你能按正确的<i>a</i>、<i>b</i>值求出方程组的解吗？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南郑州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-16", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522158602787790848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "522158602787790848", "title": "河南省郑州市枫杨、朗悦慧等九校联考2024−2025学年上学期期中八年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "522158609012137984", "questionArticle": "<p>3．（1）计算： $ \\left ( { \\sqrt { 12 }-2\\sqrt { \\dfrac { 1 } { 3 } }+\\sqrt { 48 } } \\right ) \\div \\left ( { 2\\sqrt { 3 } } \\right )  $ ；</p><p>（2）解方程组： $ \\begin{cases} 2x-5y=-21 \\\\ 4x+3y=23 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南郑州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-16", "keyPointIds": "16389|16424", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522158602787790848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "522158602787790848", "title": "河南省郑州市枫杨、朗悦慧等九校联考2024−2025学年上学期期中八年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521703307691204608", "questionArticle": "<p>4．若方程组 $ \\begin{cases} a+b=3 \\\\ b+c=2 \\\\ c+a=1 \\end{cases}  $ 的解满足 $ k=a+b+c $ ，则点 $ P(k+2,1-2k) $ 在第<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>象限．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都四中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-16", "keyPointIds": "16443|16497", "keyPointNames": "解三元一次方程组|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521703299608780800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "521703299608780800", "title": "四川省成都市石室联合中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "521703306420330496", "questionArticle": "<p>5．解方程组：</p><p>(1) $ \\begin{cases} x-y=8 \\\\ 5x+y=4 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x-1 } { 2 }=\\dfrac { y+1 } { 3 } \\\\ 2\\left ( { x-y } \\right ) =8-3y \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川成都四中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-16", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521703299608780800", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521703299608780800", "title": "四川省成都市石室联合中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521703305468223488", "questionArticle": "<p>6．若 $ x{^{m-2}}-2y{^{n+1}}=5 $ 是关于<i>x</i>、<i>y</i>的二元一次方程，则 $ m+n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都四中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-16", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521703299608780800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "521703299608780800", "title": "四川省成都市石室联合中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "523834449550680064", "questionArticle": "<p>7．解方程（组）：</p><p>（1） $ x-\\dfrac { x+1 } { 6 }=\\dfrac { 2x+1 } { 4 } $ , （2） $ \\begin{cases} x+y=45 \\\\ 2x+y=60 \\end{cases} . $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024安徽安庆 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2024-12-16", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "523834444379103232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "523834444379103232", "title": "安徽省安庆市外国语学校2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 11}, {"id": "408458085113569280", "title": "安徽省合肥市庐阳区2023-2024学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "522158608475267072", "questionArticle": "<p>8．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} x+2y=m \\\\ 2x+y=4 \\end{cases}  $ 的解满足 $ x-y=3 $ ，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南郑州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 3, "createTime": "2024-12-16", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522158602787790848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "522158602787790848", "title": "河南省郑州市枫杨、朗悦慧等九校联考2024−2025学年上学期期中八年级数学试题", "paperCategory": 1}, {"id": "507301205036539904", "title": "河南省 郑州市二七区京广实验中学2024—2025学年上学期八年级数学期中试题", "paperCategory": 1}, {"id": "506947088090963968", "title": "四川省成都市青羊区青羊实验中学2023−2024学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "522158607346999296", "questionArticle": "<p>9．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是方程组 $ \\begin{cases} ax+y=-1 \\\\ 2x-by=0 \\end{cases}  $ 的解，则<i>a＋b</i>＝（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B．﹣2C．4D．﹣4</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南郑州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2024-12-16", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522158602787790848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "522158602787790848", "title": "河南省郑州市枫杨、朗悦慧等九校联考2024−2025学年上学期期中八年级数学试题", "paperCategory": 1}, {"id": "171920998224142336", "title": "2022年七年级下册苏科版数学第十章10.2二元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "521703304331567104", "questionArticle": "<p>10．若 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是关于<i>x</i>、<i>y</i>的二元一次方程<i>ax</i>−3<i>y</i>=1的解，则<i>a</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．−5B．−1C．2D．7</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "620000|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都四中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-16", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521703299608780800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "521703299608780800", "title": "四川省成都市石室联合中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}, {"id": "220154090294124544", "title": "甘肃省武威市凉州区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 240, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 240, "timestamp": "2025-07-01T02:29:10.894Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}