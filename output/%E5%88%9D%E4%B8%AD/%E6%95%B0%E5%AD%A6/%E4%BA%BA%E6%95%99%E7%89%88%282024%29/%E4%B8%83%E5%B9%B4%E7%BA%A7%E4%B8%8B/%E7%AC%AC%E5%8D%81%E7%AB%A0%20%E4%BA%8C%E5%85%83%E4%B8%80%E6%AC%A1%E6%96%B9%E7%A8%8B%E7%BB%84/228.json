{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 227, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "526894813045628928", "questionArticle": "<p>1．解方程组：</p><p>(1) $ \\begin{cases} x+y=3 \\\\ 2x+3y=8 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3\\left ( { x-1 } \\right ) =y+5 \\\\ 5\\left ( { y-1 } \\right ) =3\\left ( { x+5 } \\right )  \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安铁一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894804258562048", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894804258562048", "title": "陕西省西安市碑林区铁一中2024—2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526894810889756672", "questionArticle": "<p>2．我国古代数学著作《增删算法统宗》记载“绳索量竿”问题，其大意为：现有一根竿和一条绳索，用绳索去量竿，绳索比竿长5尺；如果将绳索对半折后再去量竿，就比竿短5尺．设绳索长<i>x</i>尺，竿长<i>y</i>尺，则符合题意的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} x=y+5 \\\\ \\dfrac { 1 } { 2 }x=y-5 \\end{cases}  $　　　　B． $ \\begin{cases} x=y-5 \\\\ \\dfrac { 1 } { 2 }x=y+5 \\end{cases}  $</p><p>C． $ \\begin{cases} x=y+5 \\\\ 2x=y-5 \\end{cases}  $　　　　D． $ \\begin{cases} x=y-5 \\\\ 2x=y+5 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安铁一中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894804258562048", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894804258562048", "title": "陕西省西安市碑林区铁一中2024—2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526895122451046400", "questionArticle": "<p>3．如图1，在第一个天平上，物块<i>A</i>的质量等于物块<i>B</i>加上物块<i>C</i>的质量；如图2，在第二个天平上，物块<i>A</i>加上物块<i>B</i>的质量等于3个物块<i>C</i>的质量．已知物块<i>A</i>的质量为 $ 10{ \\rm{ g } } $ ．请你判断：1个物块<i>B</i>的质量是<u>&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ g } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/12/2/20/0/0/0/533644564088791041/images/img_1.png\" style='vertical-align:middle;' width=\"476\" alt=\"试题资源网 https://stzy.com\">  </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024陕西西光中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526895117241720832", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526895117241720832", "title": "陕西省西安市新城区西光中学教育集团联考2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "526895271051042816", "questionArticle": "<p>4．（1）计算： $ (\\sqrt { 6 }-\\sqrt { 2 })(1+\\sqrt { 3 })-6\\sqrt { \\dfrac { 1 } { 2 } } $ ；</p><p>（2）解方程组： $ \\begin{cases} 5x-2y-4=0 \\\\ x+y-5=0 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16389|16424", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526895263329329152", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "526895263329329152", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末数学模拟测试题一", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526895269616590848", "questionArticle": "<p>5．小颖家离学校1880米，其中有一段为上坡路，另一段为下坡路，她跑步去学校共用了16分钟，已知小颖在上坡路上的平均速度是80米/分钟，在下坡路上的平均速度是200米/分钟，设小颖上坡用了<i>x</i>分钟，下坡用了<i>y</i>分钟，根据题意列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x{ { + } }y=16 \\\\ \\dfrac { 80 } { 60 }x{ { + } }\\dfrac { 200 } { 60 }y=1880 \\end{cases}  $ B． $ \\begin{cases} x=16{ { + } }y \\\\ 80x{ { + } }200y=1880 \\end{cases}  $ </p><p>C． $ \\begin{cases} x{ { + } }y=16 \\\\ 80x=1880{ { + } }200y \\end{cases}  $ D． $ \\begin{cases} x{ { + } }y=16 \\\\ 80x{ { + } }200y=1880 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526895263329329152", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526895263329329152", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末数学模拟测试题一", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527141764001472512", "questionArticle": "<p>6．已知 $ x $ 、 $ y $ 满足方程组 $ \\begin{cases} 2x+y=5 \\\\ x+2y=4 \\end{cases}  $ ，则 $ x-y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|420000|430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024黑龙江绥化 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 4, "createTime": "2025-01-03", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141755923243008", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "527141755923243008", "title": "黑龙江省绥化市2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "444990546395832320", "title": "湖南省娄底市第三中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "423253865313640448", "title": "黑龙江省哈尔滨市第十七中学校2022-2023学年七年级下学期3月考数学试题", "paperCategory": 1}, {"id": "212534079371124736", "title": "湖北省孝感市2021-2022学年八年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527141761602330624", "questionArticle": "<p>7．足球比赛中，每场比赛都要分出胜负每队胜1场得3分，负一场扣1分，某队在8场比赛中得到12分，若设该队胜的场数为<i>x</i>负的场数为<i>y</i>，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=8 \\\\ 3x-y=12 \\end{cases}  $ B． $ \\begin{cases} x-y=8 \\\\ 3x-y=12 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=18 \\\\ 3x+y=12 \\end{cases}  $ D． $ \\begin{cases} x-y=8 \\\\ 3x+y=12 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|220000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024黑龙江绥化 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-01-03", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141755923243008", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "527141755923243008", "title": "黑龙江省绥化市2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "304185565586432000", "title": "吉林省长春市朝阳区第二实验学校2022-2023学年七年级下学期月考数学试题", "paperCategory": 1}, {"id": "128572567309295616", "title": "福建省龙岩市2020年5月九年级中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527897141265604608", "questionArticle": "<p>8．计算和解方程组：</p><p>(1) $ {\\left( { \\sqrt { 2023 }-1 } \\right) ^ {0}}-\\left  | { \\sqrt { 3 }-2 } \\right  | +{\\left( { \\dfrac { 1 } { 5 } } \\right) ^ {-1}} $ </p><p>(2) $ \\begin{cases} 2x+y=4 \\\\ 2y+1=5x \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西吉安 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16323|16372|16423", "keyPointNames": "零指数幂|负整数指数幂|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527897134026235904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527897134026235904", "title": "江西省吉安市八校联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527141560250572800", "questionArticle": "<p>9．为迎接“国家卫生城市”复检，某市环卫局准备购买<i>A</i>，<i>B</i>两种型号的垃圾箱，通过市场调研得知：购买3个<i>A</i>型垃圾箱和2个<i>B</i>型垃圾箱共需540元；购买2个<i>A</i>型垃圾箱比购买3个<i>B</i>型垃圾箱少用160元．</p><p>（1）求每个<i>A</i>型垃圾箱和<i>B</i>型垃圾箱各多少元．</p><p>（2）该市现需要购买<i>A</i>，<i>B</i>两种型号的垃圾箱共30个，其中购买<i>A</i>型垃圾箱不超过16个．</p><p>①求购买垃圾箱的总花费<i>w</i>(元)与购买<i>A</i>型垃圾箱的个数<i>x</i>之间的函数关系式；</p><p>②当购买<i>A</i>型垃圾箱多少个时总费用最少，最少费用是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南平顶山 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-03", "keyPointIds": "16438|16547", "keyPointNames": "和差倍分问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141551950045184", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "527141551950045184", "title": "河南省平顶山市等2地2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "400809856100245504", "title": "广东省普宁市2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527896741363884032", "questionArticle": "<p>10．为丰富学生的课余生活，某班计划购买若干篮球和足球．据了解，买6个篮球和10个足球需要1700元：买10个篮球和20个足球需要3100元．</p><p>(1)求每个篮球和每个足球的价格分别是多少元？</p><p>(2)该班计划恰好用3000元购买篮球和足球（两种均购买），求该班共有哪几种采购方案．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南河南实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16420|16437|16486", "keyPointNames": "二元一次方程的解|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527896733096910848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527896733096910848", "title": "河南省实验中学2024−2025学年八年级上学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 228, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 228, "timestamp": "2025-07-01T02:27:41.582Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}