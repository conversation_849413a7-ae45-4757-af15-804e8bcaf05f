{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 242, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "522522532106248192", "questionArticle": "<p>1． 20位同学在植树节这天共种了52棵树苗，其中男生每人种3棵，女生每人种2棵，设男生有<i>x</i>人，女生有<i>y</i>人，根据题意，列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=52 \\\\ 3x+2y=20 \\end{cases}  $ B． $ \\begin{cases} x+y=52 \\\\ 2x+3y=20 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=20 \\\\ 2x+3y=52 \\end{cases}  $ D． $ \\begin{cases} x+y=20 \\\\ 3x+2y=52 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|120000|410000|450000|420000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 17, "referenceNum": 6, "createTime": "2024-12-13", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473351652978688", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "579473351652978688", "title": "天津市第一中学2024—2025学年下学期七年级数学期中试卷", "paperCategory": 1}, {"id": "522522525844152320", "title": "辽宁省沈阳市第四十三中学 2024−2025学年上学期八年级12月月考数学试卷", "paperCategory": 1}, {"id": "468259015102865408", "title": "广西壮族自治区防城港市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "158696859904548864", "title": "广东省揭阳市揭东区2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "151687465685786624", "title": "湖北省恩施土家族苗族自治州咸丰县2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "129896552051548160", "title": "河南省驻马店市2019届九年级一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "521720971947974656", "questionArticle": "<p>2．若 $ 2a-b=0 $ ，且关于 $ x $ ， $ y $ 的二元一次方程 $ (a-1)x+by+5-2a=0 $ ，当 $ a $ 取不同值时，方程都有一个公共解，那么这个公共解为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A.  $ \\begin{cases}x=3,\\\\ y=-1\\end{cases} $ B.  $ \\begin{cases}x=1,\\\\ y=-\\dfrac{1}{2}\\end{cases} $ C.  $ \\begin{cases}x=2,\\\\ y=\\dfrac{3}{2}\\end{cases} $ D.  $ \\begin{cases}x=5,\\\\ y=-\\dfrac{3}{2}\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-13", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "卷2 第六章提优验收卷（B卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "521442136916533248", "questionArticle": "<p>3．为落实“绿水青山就是金山银山”的发展理念,某市政部门招标一工程队负责在山脚下修建一座水库的土方施工任务．该工程队有 $ A,B $ 两种型号的挖掘机,已知3台 $ \\mathrm{ A } $ 型和5台 $ B $ 型挖掘机同时施工一小时挖土165立方米；4台 $ \\mathrm{ A } $ 型和7台 $ B $ 型挖掘机同时施工一小时挖土225立方米．每台 $ \\mathrm{ A } $ 型挖掘机一小时的施工费用为300元,每台 $ B $ 型挖掘机一小时的施工费用为180元．</p><p>(1)分别求每台 $ \\mathrm{ A } $ 型,  $ B $ 型挖掘机一小时挖土多少立方米?</p><p>(2)若不同数量的 $ \\mathrm{ A } $ 型和 $ B $ 型挖掘机共12台同时施工4小时,至少完成1080立方米的挖土量,且总费用不超过12960元．问施工时有哪几种调配方案,并指出哪种调配方案的施工费用最低,最低费用是多少元?</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|410000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2018山东潍坊 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 3, "createTime": "2024-12-13", "keyPointIds": "16431|16543", "keyPointNames": "工程问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "1018923813703680", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "1018923813703680", "title": "山东省潍坊市2018年中考数学试题", "paperCategory": 1}, {"id": "521442129098350592", "title": "浙江省杭州市外国语学校2024—2025学年上学期八年级数学期中试题", "paperCategory": 1}, {"id": "140933127380180992", "title": "河南省鹤壁市2020年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521440910367825920", "questionArticle": "<p>4．二元一次方程组 $ \\begin{cases} y=2-x \\\\ 3x=1+2y \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-1 \\\\ y=-1 \\end{cases}  $ B． $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ C． $ \\begin{cases} x=1 \\\\ y=1 \\end{cases}  $ D． $ \\begin{cases} x=-1 \\\\ y=1 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安市曲江第一中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2024-12-13", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521440905338855424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "521440905338855424", "title": "陕西省西安市曲江第一中学2024−2025学年上学期八年级数学期中试题", "paperCategory": 1}, {"id": "248408237388636160", "title": "陕西省西安高新一中2022-2023学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "521061877910118400", "questionArticle": "<p>5．某文具店购进<i>A</i>、<i>B</i>两种型号的笔袋，两次购进笔袋的情况如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">进货批次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\"><i>A</i>型笔袋（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\"><i>B</i>型笔袋（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">总费用（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">一</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">400</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">二</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">350</p></td></tr></table><p>(1)求<i>A</i>，<i>B</i>两种型号的笔袋进价各是多少元？</p><p>(2)在销售过程中，为了增大<i>A</i>型笔袋的销售量，超市决定对<i>A</i>型笔袋进行降价销售，当销售单价为40元时，每天可以售出20个，每降价1元，每天将多售出5个，请问超市将每个<i>A</i>型笔袋降价多少元时，每天售出<i>A</i>型笔袋的利润为240元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川成都七中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2024-12-11", "keyPointIds": "16438|16463", "keyPointNames": "和差倍分问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521061868175138816", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521061868175138816", "title": "四川省成都市第七中学初中学校2024−2025学年九年级上学期11月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521062022441639936", "questionArticle": "<p>6．某厂租用 $ A $ ， $ B $ 两种型号的车给零售商运送货物，已知用 $ 2 $ 辆 $ A $ 型车和 $ 1 $ 辆 $ B $ 型车装满可运货 $ 10 $ 吨；用 $ 1 $ 辆 $ A $ 型车和 $ 2 $ 辆 $ B $ 型车装满货物一次可运货 $ 11 $ 吨；厂家现有 $ 21 $ 吨货物需要配送，计划租用 $ A $ ， $ B $ 两种型号车 $ 6 $ 辆一次配送完货物，且 $ A $ 型车至少 $ 1 $ 辆．根据以上信息，解答下列问题：</p><p>(1) $ 1 $ 辆 $ A $ 型车和 $ 1 $ 辆 $ B $ 型车都装满货物一次可分别运货多少吨？</p><p>(2)请你帮助厂家设计租车方案完成一次配送完 $ 21 $ 吨货物；</p><p>(3)若 $ A $ 型车每辆需租金 $ 80 $ 元每次， $ B $ 型车每辆需租金 $ 100 $ 元每次．请选出最省钱的租车方案，并求出最少租车费．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-11", "keyPointIds": "16434|16490", "keyPointNames": "方案问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521062015168716800", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "521062015168716800", "title": "浙江省杭州市杭州中学2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521703671282835456", "questionArticle": "<p>7．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 2x+y=3k+2 \\\\ 4x-3y=-k+5 \\end{cases}  $ ，若 $ x-2y=1 $ ，则<i>k</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\dfrac { 1 } { 4 } $ B． $ -\\dfrac { 1 } { 4 } $ C． $ \\dfrac { 1 } { 2 } $ D． $ -\\dfrac { 1 } { 2 } $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆八中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-11", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521703661610770432", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521703661610770432", "title": "重庆市第八中学校2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "521703670179733504", "questionArticle": "<p>8．计算：</p><p>(1) $ {\\left( { x-1 } \\right) ^ {2}}+\\left ( { x-2 } \\right ) \\left ( { x+3 } \\right )  $ </p><p>(2) $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} 4x+y=7 \\\\ \\dfrac { x-1 } { 2 }-\\dfrac { y } { 3 }=1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆八中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-11", "keyPointIds": "16333|16424", "keyPointNames": "整式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521703661610770432", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "521703661610770432", "title": "重庆市第八中学校2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521703671089897472", "questionArticle": "<p>9．在2024年，国家出台政策减免新能源汽车的购置税与车船税，一系列优惠政策如同春风拂面．某新能源汽车经销商购进紧凑和中级两种型号的新能源汽车，据了解3辆中级型汽车、2辆紧凑型汽车的进价共计104万元；2辆紧凑型汽车比3辆中级型汽车的进价少40万元．</p><p>(1)求中级型和紧凑型汽车两种型号汽车的进货单价；</p><p>(2)由于新能源汽车需求不断增加，该店准备购进中级型和紧凑型汽车两种型号的新能源汽车100辆，已知中级型汽车的售价为27万元/辆，紧凑型汽车的售价为20万元/辆．根据销售经验，购中级型汽车的数量不低于25辆，设购进<i>a</i>辆中级型汽车，100辆车全部售完获利<i>W</i>万元，该经销商应购进中级型和紧凑型汽车各多少辆．才能使<i>W</i>最大？<i>W</i>最大为多少万元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024陕西西安市曲江第一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-11", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899859296231424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899859296231424", "title": "陕西省西安市曲江第一学校2024−2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}, {"id": "521703661610770432", "title": "重庆市第八中学校2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521703668116135936", "questionArticle": "<p>10．现有一段长为360米的河道整治任务由<i>A</i>，<i>B</i>两工程队先后接力完成 $ {\\rm ．\\mathit{A}} $ 工程队每天整治24米，<i>B</i>工程队每天整治16米，共用时20天．设<i>A</i>工程队用的时间为<i>x</i>天，<i>B</i>工程队用的时间为<i>y</i>天，根据题意列关于<i>x</i>，<i>y</i>的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} x+y=20 \\\\ 24x+16y=360 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p><p>B． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} x+y=360 \\\\ 24x+16y=20 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p><p>C． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} x+y=20 \\\\ 16x+24y=360 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p><p>D． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} x+y=360 \\\\ 16x+24y=20 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆八中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-11", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521703661610770432", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "521703661610770432", "title": "重庆市第八中学校2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 243, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 243, "timestamp": "2025-07-01T02:29:33.387Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}