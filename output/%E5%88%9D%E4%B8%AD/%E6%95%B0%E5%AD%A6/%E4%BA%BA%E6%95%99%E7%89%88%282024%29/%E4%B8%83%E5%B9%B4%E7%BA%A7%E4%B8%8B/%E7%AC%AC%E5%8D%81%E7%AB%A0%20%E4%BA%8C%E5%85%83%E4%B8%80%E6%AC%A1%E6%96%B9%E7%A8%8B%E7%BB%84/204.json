{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 203, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "542798794263404544", "questionArticle": "<p>1．若关于 $ x $ 的不等式组 $ \\begin{cases} 7x-a\\geqslant  1 \\\\ \\dfrac { x+5 } { 3 }\\geqslant  x-1 \\end{cases}  $ 有且仅有4个整数解，且关于 $ m $ ， $ n $ 的二元一次方程组 $ \\begin{cases} m+2n=3 \\\\ 2m-2n=a \\end{cases}  $ 的解为整数，则所有满足条件的整数 $ a $ 的和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-02-09", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474764168732672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579474764168732672", "title": "重庆市长寿中学校2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "542798785493114880", "title": "重庆市第一中学校2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542798922483277824", "questionArticle": "<p>2．2024年11月11日，重庆“梁平柚”被列入第二批国家农产品地理标志，是全国三大名柚之一，特点是浓烈蜜香、纯甜嫩脆，深受消费者的喜爱．某超市按大小把“梁平柚”分成大果和小果出售．</p><p>(1)某公司为员工发福利，预计花费3050元购买大果和小果共400千克，此时大果售价为每千克8元，小果售价为每千克7元．求购买大果和小果各多少千克？</p><p>(2)由于春节临近，超市下调柚子价格，现该公司一次性购买大果、小果若干，其中大果共花费1920元，小果花费720元，已知购买大果的数量是小果的2倍，下调价格后大果比小果每千克贵1.5元．分别求大果和小果价格下调后每千克的售价？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市第一中学校 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-09", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542798912681189376", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542798912681189376", "title": "重庆市第一中学校2024−2025学年上学期期末考试九年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542799074908479488", "questionArticle": "<p>3．春节将至，小南同学决定用零花钱为家里购置一些装饰礼盒和食品礼盒，已知购买3个装饰礼盒和2个食品礼盒共需205元，购买4个装饰礼盒和1个食品礼盒共需190元．</p><p>(1)每个装饰礼盒和食品礼盒各需多少钱？</p><p>(2)临近春节商家促销，两种礼盒售价均有所调整，小南分别花费120元和320元购买装饰礼盒和食品礼盒，且购买的装饰礼盒比食品礼盒数量少 $ 50\\% $ ，每个装饰礼盒比食品礼盒售价少10元，则小南购买食品礼盒多少个？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆南开 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-09", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542799064351416320", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542799064351416320", "title": "重庆市南开中学校2024−2025学年九年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542799436696559616", "questionArticle": "<p>4．近年来，为了激发新消费潜力，拉动城市消费的新增长，长寿区“光合梦工厂”夜市在长寿时代中心逐渐盛行起来了．某文创产品的摊主从网商处购进 $ \\mathrm{ A } $ 、 $ B $ 两种文创产品进行销售，其中 $ \\mathrm{ A } $ 款产品进价为每个30元，标价为每个45元； $ B $ 款产品进价为每个25元，标价为每个37元．（注：利润 $ = $ 售价 $ - $ 进价）</p><p>(1)该摊主第一次用850元购进 $ \\mathrm{ A } $ 、 $ B $ 两种文创产品共30个，求将这些产品全部按标价售出后的利润；</p><p>(2)临近春节，该摊主打算将 $ B $ 款文创产品进行降价销售，若按照标价销售，平均每天可售出4个．经调查发现，每降价1元，平均每天可多售出2个，为了尽快地清空库存，将售价定为每个多少元时，才能使 $ B $ 款产品平均每天的销售利润为90元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆长寿 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-09", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542799423232843776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542799423232843776", "title": "重庆市长寿区2024−2025学年九年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543199380565696512", "questionArticle": "<p>5．解下列方程组：</p><p>(1) $ \\begin{cases} x+y=1 \\\\ x-2y=4 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x-2y=6 \\\\ 2x+3y=17 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州第十八中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543199372487467008", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543199372487467008", "title": "福建省福州十八中2024−2025学年七年级上学期期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542801543436738560", "questionArticle": "<p>6．问题情景：某数学兴趣小组开展了“无盖长方体纸盒的制作”实践活动．</p><p>(1)综合实践小组利用边长为30厘米的正方形纸板制作出两种不同方案的无盖长方体盒子．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/06/2/1/0/0/0/542801510846996486/images/img_20.png\" style=\"vertical-align:middle;\" width=\"304\" alt=\"试题资源网 https://stzy.com\"></p><p>①根据图1方式制作一个无盖的长方体盒子，先在纸板四角剪去四个同样大小边长为4厘米的小正方形，再沿虚线折合起来，则长方体纸盒的底面积为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>平方厘米；</p><p>②根据图2方式制作一个无盖的长方体纸盒，先在纸板上剪去一个小长方形，再沿虚线折合起来，已知 $ AB=3AD $ ，求该长方体纸盒的体积；</p><p>(2)小明按照图1的方式用边长为30厘米的正方形纸片制作了一个无盖的长方体盒子，小明想利用这个盒子研究无盖长方体的展开图，他发现其中有一种展开图外围周长为156厘米，求小明剪去的四个同样大小的小正方形的边长．（求出所有可能的情况）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建三明 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16411|16439|16587", "keyPointNames": "几何问题|几何问题|几何体的展开图", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542801533924057088", "questionFeatureName": "综合与实践题", "questionMethodName": "分类讨论思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542801533924057088", "title": "福建省三明市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543201747361112064", "questionArticle": "<p>7．某学校为了让学生体验化学实验的乐趣，决定从市场购买氯化钠溶液和硫酸铜溶液供实验使用．第一次购买40瓶氯化钠溶液和80瓶硫酸铜溶液需要500元，第二次购买20瓶氯化钠溶液和30瓶硫酸铜溶液需要200元．</p><p>(1)求每瓶氯化钠溶液与硫酸铜溶液的售价分别为多少元？</p><p>(2)为了加大培养学生对化学的兴趣，学校决定再次购买这两种溶液，调查发现配置每瓶硫酸铜溶液的成本是 $ a $ 元，每瓶氯化钠溶液的成本是 $ 0.5a $ 元，已知第三次购买硫酸铜的数量比第一次购买的数量少 $ 5a $ 瓶，购买的氯化钠溶液的数量是第一次的2倍，商场获利330元，求 $ a $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543201737429000192", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "543201737429000192", "title": "重庆市巴川中学校2024−2025学年九年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543201147172986880", "questionArticle": "<p>8．我国清代算书《御制数理精蕴》中有这样一题：“设如砚七方比笔三支价多四百八十文，又砚三方比笔九支价少一百八十文，问笔砚价各若干？”其大意为假设七方砚台的价格比三支笔的价格多出四百八十文钱，而三方砚台的价格则比九支笔的价格少了一百八十文钱，请问笔和砚台的单价分别是多少？</p><p>(1)求笔和砚台的单价．</p><p>(2)为落实立德树人的根本任务，某校开设了书法课程，需购买砚台和笔若干，已知笔的数量是砚台数量的2倍，学校共花费3420元．问该校可以购买砚台和笔各多少？（1文约等于1.2元）</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543201137593196544", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "543201137593196544", "title": "陕西省榆林市2024—2025学年上学期八年级期末数学教学素养测评（四）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543201144660598784", "questionArticle": "<p>9．解方程组： $ \\begin{cases} 2x+y=3 \\\\ x+3y=-1 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543201137593196544", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "543201137593196544", "title": "陕西省榆林市2024—2025学年上学期八年级期末数学教学素养测评（四）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543201143872069632", "questionArticle": "<p>10．小逸的爸爸比小逸大27岁，5年前小逸的爸爸的年龄是小逸的10倍，设小逸现在的年龄为<i>x</i>岁，小逸的爸爸现在的年龄为<i>y</i>岁，根据题意可列方程组：<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16436", "keyPointNames": "年龄问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543201137593196544", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "543201137593196544", "title": "陕西省榆林市2024—2025学年上学期八年级期末数学教学素养测评（四）", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 204, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 204, "timestamp": "2025-07-01T02:24:55.548Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}