{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 221, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "534115614505345024", "questionArticle": "<p>1．解下列的二元一次方程组</p><p>(1) $ \\begin{cases} 2x+y=7 \\\\ 3x-y=5 \\end{cases}  $ </p><p>(2) $ \\begin{cases} \\dfrac { x-y } { 2 }-\\dfrac { x+y } { 4 }=-1 \\\\ x+y=-8 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东济外 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-01-14", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534115606116737024", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534115606116737024", "title": "山东省济南市历城区济南外国语学校2024−2025学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "521440905338855424", "title": "陕西省西安市曲江第一中学2024−2025学年上学期八年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534115611791630336", "questionArticle": "<p>2．若 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是关于<i>x</i> 、<i>y</i>的二元一次方程<i>ax</i>−2<i>y</i>＝1的解，则<i>a</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．5C．−3D．−5</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025山东济外 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-01-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534115606116737024", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534115606116737024", "title": "山东省济南市历城区济南外国语学校2024−2025学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "527584030084079616", "title": "山东省济南外国语学校2024−2025学年上学期12月份月考八年级数学试题", "paperCategory": 1}, {"id": "159594391312572416", "title": "山东省济南市天桥区2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "534115722894548992", "questionArticle": "<p>3．习近平总书记说：“读书可以让人保持思想活力，让人得到智慧启发，让人滋养浩然正气．”某校为提高学生的阅读品味，现决定购买获得矛盾文学奖的甲、乙两种书共100本，已知购买2本甲种书和1本乙种书共需100元，购买3本甲种书和2本乙种书共需165元．</p><p>(1)求甲，乙两种书的单价分别为多少元？</p><p>(2)若学校决定购买以上两种书的总费用不超过3200元，那么该校最多可以购买甲种书多少本？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|510000|430000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2023四川眉山 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 5, "createTime": "2025-01-14", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "326389611151794176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "326389611151794176", "title": "2023年四川省眉山市中考数学真题", "paperCategory": 1}, {"id": "372015118878547968", "title": "2023年四川省眉山市中考数学真题", "paperCategory": 1}, {"id": "583055826434174976", "title": "2025年湖南省长沙市雅礼教育集团一模数学试题", "paperCategory": 1}, {"id": "534115713440587776", "title": "山东省济南市市中区济南育英中学2024−2025学年八年级上学期期末数学模拟试题", "paperCategory": 1}, {"id": "355660208742375424", "title": "广东省深圳市福田区八校2023-2024学年九年级上学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534115719656546304", "questionArticle": "<p>4．如图，宽为 $ 50cm $ 的长方形图案由10个全等的小长方形拼成，其中一个小长方形的面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/20/2/20/0/0/0/536525572739997696/images/img_1.png\" style='vertical-align:middle;' alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 400cm{^{2}} $</p><p>B． $ 500cm{^{2}} $</p><p>C． $ 600cm{^{2}} $</p><p>D． $ 300cm{^{2}} $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|-1|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东济南 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 8, "referenceNum": 3, "createTime": "2025-01-14", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534115713440587776", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534115713440587776", "title": "山东省济南市市中区济南育英中学2024−2025学年八年级上学期期末数学模拟试题", "paperCategory": 1}, {"id": "462769670933749760", "title": "湖南省长沙市一中岳麓中学2023-2024学年七年级下学期第三次月考数学试题", "paperCategory": 1}, {"id": "171932129005182976", "title": "苏科版七年级下册第10章二元一次方程组单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "532312268266053632", "questionArticle": "<p>5．在平面直角坐标系中，已知点 $ M(n-2,2m-7) $ ，点 $ N(m,3) $ ．</p><p>(1)若<i>M</i>在<i>x</i>轴上，求<i>m</i>的值；</p><p>(2)若 $ MN/\\mskip-4mu/y $ 轴，点<i>M</i>在点<i>N</i>的下方且 $ MN=2 $ ，求出点<i>M</i>的坐标．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-13", "keyPointIds": "16423|16497|16499", "keyPointNames": "代入消元法解二元一次方程组|点的坐标|坐标确定位置", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312258984058880", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312258984058880", "title": "江苏省扬州市2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "522051226688593920", "title": "江苏省扬州市2024−2025学年八年级上学期12月月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "532312518301097984", "questionArticle": "<p>6．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x+2y-6=0 \\\\ x-2y+mx+4m=0 \\end{cases}  $ </p><p>(1)若方程组的解满足 $ x+y=0 $ ，求 $ m $ 的值；</p><p>(2)无论实数 $ m $ 取何值，方程 $ x-2y+mx+4m=0 $ 总有一个固定的解，请求出这个解？</p><p>(3)若方程组的解中 $ x $ 为整数，且 $ m $ 是自然数，求 $ m $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江西抚州一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312511254667264", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312511254667264", "title": "江西省抚州市第一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532312517927804928", "questionArticle": "<p>7．目前，国内旅游市场回暖，某海边景区积极部署，为暑假学生海边游作充足的准备，而其中遮阳伞在往年供不应求，经调查该景区准备购买<i>A</i>、<i>B</i>两种型号的遮阳伞供景区使用．已知购买5个<i>A</i>型号和2个<i>B</i>型号遮阳伞的需要2500元，购买3个<i>A</i>型号和1个<i>B</i>型号的遮阳伞需要1400元．</p><p>(1)求<i>A</i>，<i>B</i>两个型号遮阳伞的单价；</p><p>(2)经调查，该景区需要添置遮阳伞200个，且要求<i>A</i>型号的数量不能超过<i>B</i>型号的数量，景区的预算6万元够用吗?若不够，请说明理由，并算出怎样购买才能使花费最低?最低费用是多少?</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江西抚州一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16424|16438|16535", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312511254667264", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312511254667264", "title": "江西省抚州市第一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532312516807925760", "questionArticle": "<p>8．（1）计算： $ \\left  | { 1-\\sqrt { 3 } } \\right  | +{\\left( { { \\rm{ π } }-3.14 } \\right) ^ {0}}-\\dfrac { \\sqrt { 6 } } { \\sqrt { 2 } } $ ；</p><p>（2）解方程组： $ \\begin{cases} 3x-2y=9 \\\\ x+2y=3 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西抚州一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16299|16424", "keyPointNames": "实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312511254667264", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312511254667264", "title": "江西省抚州市第一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532312517432877056", "questionArticle": "<p>9．已知关于<i>x</i>、<i>y</i>的方程组 $ \\begin{cases} 2x-3y=3 \\\\ ax+by=-1 \\end{cases}  $ 和 $ \\begin{cases} 3x+2y=11 \\\\ 2ax+3by=3 \\end{cases}  $ 的解相同，求 $ {\\left( { 3a+b } \\right) ^ {2024}} $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江西抚州一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16424|30400", "keyPointNames": "加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312511254667264", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312511254667264", "title": "江西省抚州市第一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532312515356696576", "questionArticle": "<p>10．若关于<i>x</i>，<i>y</i>的方程 $ ax-3y=2 $ 有一组解是 $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ 则<i>a</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -8 $ B．8C． $ -10 $ D．2</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西抚州一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-13", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532312511254667264", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532312511254667264", "title": "江西省抚州市第一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 222, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 222, "timestamp": "2025-07-01T02:26:57.497Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}