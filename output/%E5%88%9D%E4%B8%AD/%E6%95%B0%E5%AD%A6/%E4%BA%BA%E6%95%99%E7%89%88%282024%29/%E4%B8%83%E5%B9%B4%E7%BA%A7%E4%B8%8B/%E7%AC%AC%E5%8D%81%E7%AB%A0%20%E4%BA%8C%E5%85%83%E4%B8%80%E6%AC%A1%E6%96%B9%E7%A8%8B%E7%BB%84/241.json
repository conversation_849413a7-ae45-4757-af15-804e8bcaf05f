{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 240, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "522162956433399808", "questionArticle": "<p>1．今年的“双11”商战火爆，各大商家积极促销．某社区准备采购文化墙贴和小书柜来更新社区设施，发现购买5张文化墙贴和4个小书柜共需1450元；若购买6张文化墙贴和3个小书柜共需1200元．</p><p>(1)求出采购1张文化墙贴和1个小书柜，各需要多少钱？</p><p>(2)经测算，除了采购一部分新的小书柜，还可以分两次对现有的部分小书柜进行修复翻新，会减少一些开支．若第一次翻新部分旧的小书柜的费用为4000元，第二次准备翻新余下旧的小书柜时，发现翻新1个小书柜的成本上涨了 $ 20{ \\rm{ \\% } } $ ，第二次翻新余下旧的小书柜的费用是3600元，且第二次翻新旧的小书柜的数量比第一次翻新旧的小书柜的数量少10个．那么翻新1个旧的小书柜需要多少元？本次社区打算购买30张文化墙贴、采购15个新的小书柜和翻新全部旧的小书柜，那么社区在更换社区设施上，投入了多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市育才中学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-15", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522162947038158848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "522162947038158848", "title": "重庆市育才中学校2024−2025学年九年级上学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "522522790257270784", "questionArticle": "<p>2．为了进一步提升学生体质健康水平，某校计划用640元购买12个体育用品，备选体育用品及单价如表所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">备选体育用品</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">足球</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">篮球</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">排球</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">单价（元/个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">80</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">40</p></td></tr></table><p>(1)若640元全部用来购买足球和排球，求足球和排球各买多少个？</p><p>(2)若学校先用一部分资金购买了 $ m $ 个排球，再用剩下的资金购买了足球和篮球（足球和篮球的购买个数相同），此时正好剩余40元，求 $ m $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东潍坊 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-15", "keyPointIds": "16416|16441", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522522781868662784", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "522522781868662784", "title": "山东省潍坊市安丘市多校联考2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "522522102504660992", "questionArticle": "<p>3．校园举行足球比赛，胜一场得3分，平一场得1分，输一场得0分；七年一班共进行了12场比赛，获得19分，则七年一班获胜的场数有几种情况（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1种　　　　B．2种　　　　C．3种　　　　D．4种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024黑龙江佳木斯 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "522522093306552320", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "522522093306552320", "title": "黑龙江省佳木斯市2024−2025学年九年级上学期11月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "521719427647184896", "questionArticle": "<p>4．下列方程组中，属于二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A.  $ \\begin{cases}x+y=-200,\\\\ x-z=-100\\end{cases} $ B.  $ \\begin{cases}m+n=2,\\\\ n=-25\\end{cases} $ </p><p>C.  $ \\begin{cases}x+y=3,\\\\ {x}^{2}-y=8\\end{cases} $ D.  $ \\begin{cases}m+n=19,\\\\ mn=90\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025福建泉州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-15", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115954417868800", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}, {"id": null, "title": "卷1 第六章基础诊断卷（A卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "521719432323833856", "questionArticle": "<p>5．（本小题满分8分）已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases}x-y=4a-3,\\\\ x+2y=-5a.\\end{cases} $ </p><p>（1） 当这个方程组的解 $ x $ ， $ y $ 的值互为相反数时，求 $ a $ 的值；</p><p>（2） 说明无论 $ a $ 取什么值， $ 3x+y $ 的值始终不变.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2022江苏苏州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2024-12-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "196603063896940544", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "196603063896940544", "title": "江苏省苏州市太仓市第一中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": null, "title": "卷1 第六章基础诊断卷（A卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "521719431283646464", "questionArticle": "<p>6．若关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases}x-y=3k,\\\\ x+y=5k\\end{cases} $ 的解也是二元一次方程 $ 2x-3y=10 $ 的解，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024天津滨海新 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 2, "createTime": "2024-12-14", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "467873553041367040", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "467873553041367040", "title": "天津市滨海新区2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": null, "title": "卷1 第六章基础诊断卷（A卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "5"}, {"questionId": "521719428033060864", "questionArticle": "<p>7．已知关于 $ x $ ， $ y $ 的二元一次方程 $ 3x-ky=7 $ 有一组解为 $ \\begin{cases}x=3,\\\\ y=2,\\end{cases} $  则 $ k $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A. 1B.  $ -1 $ C.  $ -\\dfrac{4}{3} $ D.  $ -4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024河北廊坊 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2024-12-14", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "455856010369998848", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "455856010369998848", "title": "河北省廊坊市安次区第四中学2023-2024学年七年级下学期月考数学试题", "paperCategory": 1}, {"id": null, "title": "卷1 第六章基础诊断卷（A卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "521719427856900096", "questionArticle": "<p>8．已知方程组 $ \\begin{cases}x+2y=5,\\\\ 2x+y=7,\\end{cases} $  则 $ x-y $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A. 2B.  $ -2 $ C. 0D.  $ -1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|110000|430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河南许昌 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 4, "createTime": "2024-12-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577162062788337664", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "577162062788337664", "title": "河南省许昌市2024−2025学年下学期期末七年级数学试卷", "paperCategory": 11}, {"id": "449183571674177536", "title": "北京市清华大学附属中学望京学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "462769590768017408", "title": "湖南省长沙市立信中学2023-2024学年七年级下学期第三次月考数学试题", "paperCategory": 1}, {"id": null, "title": "卷1 第六章基础诊断卷（A卷）2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "521720188418433024", "questionArticle": "<p>9．一个两位数，个位上的数字与十位上的数字之和为7，如果这个两位数加上45后恰好等于个位数字与十位数字对调后组成的新两位数，则原来的两位数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A. 61B. 52C. 16D. 25</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2024-12-14", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第十章 对点上分《2025春初中上分卷 数学七年级下册 人教版》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "521720187315331072", "questionArticle": "<p>10．在解方程组 $ \\begin{cases}ax+5y=10,\\\\ 4x-by=-4\\end{cases} $ 时，由于粗心，甲看错了方程组中的 $ a $ ，得到的解为 $ \\begin{cases}x=-3,\\\\ y=-1,\\end{cases} $  乙看错了方程组中的 $ b $ ，得到的解为 $ \\begin{cases}x=5,\\\\ y=4,\\end{cases} $  则原方程组的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2022河北张家口 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 15, "referenceNum": 2, "createTime": "2024-12-14", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "208521819774558208", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "208521819774558208", "title": "河北省张家口市宣化区2021-2022学年七年级下学期期末考试数学（冀教版）试题", "paperCategory": 1}, {"id": null, "title": "第六章 对点上分2025春 初中必刷题 数学七年级下册 冀教版", "paperCategory": 2}], "questionTypeCode": "5"}]}}, "requestData": {"pageNum": 241, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 241, "timestamp": "2025-07-01T02:29:18.372Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}