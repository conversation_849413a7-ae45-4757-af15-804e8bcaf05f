{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 205, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "542360341801377792", "questionArticle": "<p>1．某商店购进某种茶壶、茶杯共200个进行销售，其中茶杯的数量是茶壶数量的5倍还多20个．销售方式有两种：（1）单个销售；（2）成套销售．相关信息如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>进价（元/个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>单个售价（元/个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>成套售价（元/套）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>茶壶</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>24</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>a</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>55</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>茶杯</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>a</i>﹣30</p></td></tr><tr><td colspan=\"4\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>备注：（1）一个茶壶和和四个茶杯配成一套（如图）；</p><p>（2）利润=（售价﹣进价）×数量</p></td></tr></table><p>（1）该商店购进茶壶和茶杯各有多少个;</p><p>（2）已知甲顾客花180元购买的茶壶数量与乙顾客花30元购买的茶杯数量相同．</p><p>①求表中<i>a</i>的值;</p><p>②当该商店还剩下20个茶壶和100个茶杯时，商店将这些茶壶和茶杯中的一部分按成套销售，其余按单个销售，这120个茶壶和<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/05/2/1/0/0/0/542360293826928645/images/img_18.png\" style=\"vertical-align:middle;\" width=\"5\" alt=\"试题资源网 https://stzy.com\">茶杯全部售出后所得的利润为365元．问成套销售了多少套.</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/05/2/1/0/0/0/542360293831122944/images/img_19.jpg\" style=\"vertical-align:middle;\" width=\"136\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南河南师大附中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-07", "keyPointIds": "16432|16437|16438", "keyPointNames": "配套问题|销售利润问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542360332099952640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542360332099952640", "title": "河南省新乡市河南师范大学附属中学等学校2024−2025学年八年级上学期1月期末联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542360625718009856", "questionArticle": "<p>2．为培养学生的数学阅读习惯，激发学生学习数学的兴趣，某校计划购买甲、乙两种数学课外读物供学生阅读．已知购买1本甲种和1本乙种数学课外读物共需 $ 108 $ 元，购买1本甲种和2本乙种数学课外读物共需 $ 180 $ 元．</p><p>(1)求甲、乙两种数学课外读物的单价；</p><p>(2)该校计划购买甲、乙两种数学课外读物共 $ 50 $ 本，总费用不超过 $ 2520 $ 元，那么至少可购买甲种数学课外读物多少本？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西丰中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-07", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542360614733127680", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542360614733127680", "title": "江西省宜春市丰城市丰城中学2024−2025学年九年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542360621104275456", "questionArticle": "<p>3． $ 《 $ 九章算术 $ 》 $ 中记载．“今有人共买物，人出八，盈三；人出七，不足四．问人数、物价各几何？”其大意是：“现有一些人共同买一个物品，每人出 $ 8 $ 钱，还盈余 $ 3 $ 钱；每人出 $ 7 $ 钱，还差 $ 4 $ 钱，问人数、物品价格各是多少？”设人数为 $ x $ 人，物品的价格为 $ y $ 钱，根据题意，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=8x-3 \\\\ y=7x+4 \\end{cases}  $　　　　B． $ \\begin{cases} x=8y+3 \\\\ x=7y-4 \\end{cases}  $　　　　C． $ \\begin{cases} y=8x+3 \\\\ y=7x-4 \\end{cases}  $　　　　D． $ \\begin{cases} x=8y-3 \\\\ x=7y+4 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西丰中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-02-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542360614733127680", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542360614733127680", "title": "江西省宜春市丰城市丰城中学2024−2025学年九年级上学期1月期末数学试题", "paperCategory": 1}, {"id": "464293417054412800", "title": "湖南省怀化市2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542361328683360256", "questionArticle": "<p>4．为了增强中学生体质，某学校倡导学生在大课间开展打羽毛球活动，需购买甲、乙两种品牌羽毛球．已知购买甲种品牌羽毛球12个和乙种品牌羽毛球6个共需240元；购买甲种品牌羽毛球15个和乙种品牌羽毛10个共需325元．</p><p>(1)购买一个甲种品牌羽毛球和一个乙种品牌羽毛球各需要多少元？</p><p>(2)若购买甲乙两种品牌羽毛球共花费1800元，甲种品牌羽毛球数量不低于乙种品牌羽毛球数量的5倍且不超过乙种品牌羽毛球数量的16倍，则共有几种购买方案？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都四中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361318054993920", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361318054993920", "title": "四川省成都市石室联合中学2024−2025学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542361325705404416", "questionArticle": "<p>5．计算或解方程组：</p><p>(1) $ \\sqrt { 6 }\\times \\sqrt { \\dfrac { 2 } { 3 } }-{\\left( { -\\dfrac { 1 } { 2 } } \\right) ^ {0}}-\\left  | { \\sqrt { 3 }-2 } \\right  |  $ ；</p><p>(2) $ \\begin{cases} x+y=4 \\\\ x-2y=1 \\end{cases}  $ ；</p><p>(3) $ \\begin{cases} 3-x\\geqslant  2\\left ( { x-3 } \\right )  \\\\ \\dfrac { x-1 } { 2 }-\\dfrac { x+1 } { 3 } > -1 \\end{cases}  $ 并把其解集表示在数轴上．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/10/2/20/0/0/0/544161730525437953/images/img_1.png\" style='vertical-align:middle;' width=\"237\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都四中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16299|16424|16489|28266", "keyPointNames": "实数的运算|加减消元法解二元一次方程组|解一元一次不等式组|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361318054993920", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361318054993920", "title": "四川省成都市石室联合中学2024−2025学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542361323792801792", "questionArticle": "<p>6．我国古代问题：以绳测井，若将绳三折测之，绳多四尺，若将绳四折测之，绳多一尺，绳长、井深各几何？这段话的意思是，用绳子量井深，把绳三折来量，井外余绳四尺，把绳四折来量，井外余绳一尺，绳长、井深各几尺？若设绳长为<i>x</i>尺，井深为<i>y</i>尺，则符合题意的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x+y=4 \\\\ 4x+y=1 \\end{cases}  $　　　　B． $ \\begin{cases} 3x-y=4 \\\\ 4x-y=1 \\end{cases}  $　　　　C． $ \\begin{cases} \\dfrac { 1 } { 3 }x+y=4 \\\\ \\dfrac { 1 } { 4 }x+y=1 \\end{cases}  $　　　　D． $ \\begin{cases} \\dfrac { 1 } { 3 }x-y=4 \\\\ \\dfrac { 1 } { 4 }x-y=1 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都四中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361318054993920", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361318054993920", "title": "四川省成都市石室联合中学2024−2025学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542361240493924352", "questionArticle": "<p>7．我国的机器狗技术处于世界领先地位，为了落实科技利民，峨眉山景区计划购买一批机器狗用于搬运物资，已知1台小型机器狗和2台大型机器狗一次可以运载400千克物资，10台小型机器狗和5台大型机器狗一次可以运载1750千克物资．</p><p>(1)求每台小型机器狗和大型机器狗每次各能运载多少千克物资？</p><p>(2)现峨眉山景区每天需运载5000千克物资，两种机器狗每天只能运送一次．已知小型机器狗每台3000元，大型机器狗每台6000元，合理购买大小机器狗数量刚好能够满足运输需求，若购买小型机器狗<i>x</i>台，求购买机器狗的总费用<i>y</i>与<i>x</i>的函数关系式．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16441|16536", "keyPointNames": "其他问题|列一次函数解析式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361230805082112", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361230805082112", "title": "四川省成都市青羊区树德实验学校2024−2025学年上学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542361237792792576", "questionArticle": "<p>8．计算题：</p><p>(1) $ \\sqrt { 81 }+{\\left( { { \\rm{ π } }-\\sqrt { 3 } } \\right) ^ {0}}-\\sqrt { 5 }+|2-\\sqrt { 5 }| $ ；</p><p>(2) $ -1{^{2024}}+\\sqrt { {\\left( { -2 } \\right) ^ {2}} }+|\\sqrt { 3 }-2|+\\sqrt[3] { -27 } $ ；</p><p>(3) $ \\left ( { 3\\sqrt { 12 }-2\\sqrt { 6 }+\\sqrt { 48 } } \\right ) \\div \\left ( { 2\\sqrt { 3 } } \\right )  $ ；</p><p>(4)解二元一次方程组： $ \\begin{cases} 4x+3y=5 \\\\ x-2y=4 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16323|16389|16424", "keyPointNames": "零指数幂|二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361230805082112", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361230805082112", "title": "四川省成都市青羊区树德实验学校2024−2025学年上学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542361040144605184", "questionArticle": "<p>9．某中学组织学生春游，原计划租用45座客车若干辆，但有15人没有座位；若租用同样数量的60座客车，则多出一辆车，且其余客车恰好坐满，已知45座客车每日每辆租金为220元，60座客车每日每辆租金为300元．试问：</p><p>(1)春游学生共多少人？原计划45座客车多少辆？</p><p>(2)若租用同一种车，要使每位同学都有座位，应该怎样租用才合算？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西实验中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361031835688960", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361031835688960", "title": "陕西省咸阳实验中学2024−2025学年八年级上学期期末质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542361039347687424", "questionArticle": "<p>10．解下列方程组：</p><p>(1) $ \\begin{cases} 3s-7t=1 \\\\ 5s-4t=17 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x-1 } { 2 }+\\dfrac { y+2 } { 3 }=\\dfrac { 3 } { 2 } \\\\ \\dfrac { x+1 } { 3 }-\\dfrac { y-1 } { 2 }=1 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西实验中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361031835688960", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361031835688960", "title": "陕西省咸阳实验中学2024−2025学年八年级上学期期末质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 206, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 206, "timestamp": "2025-07-01T02:25:08.547Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}