{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 212, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "536617979976392704", "questionArticle": "<p>1．我国古代数学名著《孙子算经》中记载：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，绳多一尺，本长几何？”意思是：用一根绳子去量一根木条，绳子剩余 $ 4.5 $ 尺；将绳子对折再量木条，木条短1尺．木条长多少尺？如果设木条长<i>x</i>尺，绳子长<i>y</i>尺，那么可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=x+4.5 \\\\ \\dfrac { 1 } { 2 }y=x-1 \\end{cases}  $ B． $ \\begin{cases} y=x+4.5 \\\\ y=2x-1 \\end{cases}  $ C． $ \\begin{cases} y=x+4.5 \\\\ \\dfrac { 1 } { 2 }y=x+1 \\end{cases}  $ D． $ \\begin{cases} y=x-4.5 \\\\ y=2x+1 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁沈阳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617974691569664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "536617974691569664", "title": "辽宁省沈阳市浑南区东北育才学校2024−2025学年九年级上学期1月期末数学试题", "paperCategory": 1}, {"id": "399278535276273664", "title": "陕西师范大学附属中学2022-2023学年八年级上学期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "536618225351565312", "questionArticle": "<p>2．某服装经销商计划购进<i>A</i>型、<i>B</i>型两种型号的童装．若购进8件<i>A</i>型童装和5件<i>B</i>型童装需用2200元；若购进4件 $ \\mathrm{ A } $ 型童装和6件<i>B</i>型童装需用1520元．</p><p>(1)求每件<i>A</i>型童装和每件<i>B</i>型童装的进价各多少元；</p><p>(2)该经销商计划用不超过11800元成本，购进<i>A</i>型童装和<i>B</i>型童装共75件，其中<i>A</i>型童装不超过35件．假若<i>A</i>型童装的定价为298元；<i>B</i>型童装的定价为198元，且全部以定价售完该批童装．该经销商获得最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西宝鸡 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-23", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536618213284552704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536618213284552704", "title": "陕西省宝鸡市凤翔区2024−2025学年八年级上学期期末质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536618223438962688", "questionArticle": "<p>3．解方程组： $ \\begin{cases} 5x-2y=17 \\\\ \\dfrac { x } { 2 }+\\dfrac { y } { 3 }=\\dfrac { 13 } { 2 } \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西宝鸡 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536618213284552704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536618213284552704", "title": "陕西省宝鸡市凤翔区2024−2025学年八年级上学期期末质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536618222121951232", "questionArticle": "<p>4．已知 $ \\begin{cases} x=-2 \\\\ y=1 \\end{cases}  $ 是二元一次方程 $ x+ay=5 $ 的一个解，则 $ a $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025陕西宝鸡 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-23", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536618213284552704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536618213284552704", "title": "陕西省宝鸡市凤翔区2024−2025学年八年级上学期期末质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "536618220997877760", "questionArticle": "<p>5．《孙子算经》是中国古代重要的数学著作，该书第三卷记载：“今有兽六首四足，禽四首二足有七十六首，下有四十六足，问兽、禽各几何？”译文：今有一种6头4脚的兽与一种4头2脚的鸟，若兽与鸟共有76个头与46只脚．问兽、鸟各有多少？设兽<i>x</i>只，鸟有<i>y</i>只，根据题意列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 6x+4y=76 \\\\ 4x+2y=46 \\end{cases}  $ B． $ \\begin{cases} 4x+6y=76 \\\\ 2x+4y=46 \\end{cases}  $ C． $ \\begin{cases} 6x+4y=46 \\\\ 4x+2y=76 \\end{cases}  $ D． $ \\begin{cases} 4x+6y=46 \\\\ 2x+4y=76 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西宝鸡 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536618213284552704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536618213284552704", "title": "陕西省宝鸡市凤翔区2024−2025学年八年级上学期期末质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537019129959587840", "questionArticle": "<p>6．解方程组：</p><p>(1) $ \\begin{cases} x=y+1① \\\\ 3x-4y=-2② \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=1① \\\\ 3x+2y=10② \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-01-23", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537019123483582464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537019123483582464", "title": "广东省深圳市罗湖区深圳中学2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}, {"id": "469008727045087232", "title": "河南省周口市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536617635854721024", "questionArticle": "<p>7．《算法统宗》中有这样一首诗：</p><p>巍巍古寺在山中，不知寺内几多僧，三百六十四只碗，恰合用尽不差争．</p><p>三人共食一碗饭，四人共尝一碗羹，请问先生能算者，都来寺内几多僧．</p><p>请用一元一次方程或者二元一次方程组求解上述问题．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南娄底 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-23", "keyPointIds": "16416|16441", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617627818434560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536617627818434560", "title": "湖南省娄底市冷水江市2024−2025学年七年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536617635456262144", "questionArticle": "<p>8．解下列方程或方程组：</p><p>(1) $ \\dfrac { x-3 } { 2 }+\\dfrac { -x+6 } { 3 }-\\dfrac { 2x+1 } { 4 }=1 $ ；</p><p>(2) $ \\begin{cases} 2x-3y=24 \\\\ \\dfrac { \\left ( { 5x+15y } \\right ) -5 } { 2 }=0 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南娄底 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-23", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617627818434560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536617627818434560", "title": "湖南省娄底市冷水江市2024−2025学年七年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537018957045211136", "questionArticle": "<p>9．利用两块长方体木块测量一张桌子的高度．首先按图①方式放置，再交换两木块的位置，按图②方式放置．测量的数据如图，则桌子的高度是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>cm；</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/21/2/1/0/0/0/537018918545694725/images/img_6.png\" style=\"vertical-align:middle;\" width=\"286\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|340000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025安徽马鞍山 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-01-23", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018947784187904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537018947784187904", "title": "安徽省马鞍山市第七中学2024—2025学年上学期期末考试七年级数学测试", "paperCategory": 1}, {"id": "423253607057760256", "title": "黑龙江省哈尔滨市第六十九中学校2022一2023学年七年级下学期三月学科活动数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537437276697042944", "questionArticle": "<p>10．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+3y=5 \\\\ 2x-3z=3 \\end{cases}  $ B． $ \\begin{cases} m+n=0 \\\\ \\dfrac { m } { 6 }+\\dfrac { 2n } { 3 }=1 \\end{cases}  $ C． $ \\begin{cases} m+n=5 \\\\ mn+n=6 \\end{cases}  $ D． $ \\begin{cases} 3x+2y=10 \\\\ x+\\dfrac { 2 } { y }=6 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025陕西西安铁一中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-01-23", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537437271684849664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537437271684849664", "title": "陕西省西安市铁一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}, {"id": "502233406681423872", "title": "四川省成都市温江区东辰外国语学校2024—−2025学年八年级上学期10月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 213, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 213, "timestamp": "2025-07-01T02:25:55.123Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}