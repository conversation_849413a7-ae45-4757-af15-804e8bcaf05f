{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 200, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544318076575064064", "questionArticle": "<p>1．解方程组： $ \\begin{cases}2x+4y-3z=2,\\\\ 4x+7y+z=3,\\\\ 8x+3y-2z=-5.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 11, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318075887198208", "questionArticle": "<p>2．在等式 $ y=a{x}^{2}+bx+c $ 中，当 $ x=1 $ 时， $ y=-2 $ ；当 $ x=-1 $ 时， $ y=20 $ ；当 $ x=2 $ 时， $ y=5 $ ，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ c= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318075677483008", "questionArticle": "<p>3．解三元一次方程组 $ \\begin{cases}x+y+z=3,\\mathrm{①}\\\\ 3x+2y+z=10,\\mathrm{②}\\\\ 2x-y+z=-1,\\mathrm{③}\\end{cases} $  若要消掉未知数 $ z $ ，则应对方程组进行的变形为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\mathrm{①}+\\mathrm{③} $ ， $ \\mathrm{①}×2-\\mathrm{②} $ B． $ \\mathrm{①}+\\mathrm{③} $ ， $ \\mathrm{③}×2+\\mathrm{②} $ </p><p>C． $ \\mathrm{②}-\\mathrm{①} $ ， $ \\mathrm{②}-\\mathrm{③} $ D． $ \\mathrm{①}-\\mathrm{②} $ ， $ \\mathrm{①}×2-\\mathrm{③} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318075463573504", "questionArticle": "<p>4．三元一次方程 $ x-y+z=3 $ 有无数个解，下列四组值中不是该方程的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x=1,\\\\ y=1,\\\\ z=3\\end{cases} $ B． $ \\begin{cases}x=2,\\\\ y=1,\\\\ z=2\\end{cases} $ C． $ \\begin{cases}x=2,\\\\ y=3,\\\\ z=4\\end{cases} $ D． $ \\begin{cases}x=3,\\\\ y=2,\\\\ z=1\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318163770449920", "questionArticle": "<p>5．若 $ |x-2y+1|+|x+y-5|=0 $ ，则 $ 2y-x= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318075270635520", "questionArticle": "<p>6．下列方程组中，不是三元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x=5,\\\\ x+y=7,\\\\ x+y+z=6\\end{cases} $ B． $ \\begin{cases}x+y+z=2,\\\\ x-y+z=0,\\\\ x-z=4\\end{cases} $ </p><p>C． $ \\begin{cases}x+y=3,\\\\ y+z=5,\\\\ x+z=4\\end{cases} $ D． $ \\begin{cases}x+y+3z=5,\\\\ x-3y=4z,\\\\ xy+yz=1\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318163506208768", "questionArticle": "<p>7．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases}ax+by=1,\\\\ ax-by=5\\end{cases} $ 的解是 $ \\begin{cases}x=3,\\\\ y=-2,\\end{cases} $ 则 $ {a}^{2023}+2{b}^{2023} $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318074981228544", "questionArticle": "<p>8．下列方程中，属于三元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\mathrm{\\pi }+x+y=6 $ B． $ xy+y+z=7 $ </p><p>C． $ x+2y-3z=9 $ D． $ 3x+2y-4z=4x+2y-2z $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318163241967616", "questionArticle": "<p>9．《九章算术》中记载有这样一个问题：“今有甲、乙二人持钱不知其数.甲得乙半而钱五十，乙得甲太半而亦钱五十.问甲、乙持钱各几何？”题目大意如下：甲、乙两人各带了若干钱.如果甲得到乙所有钱的一半，那么甲共有50钱.如果乙得到甲所有钱的三分之二，那么乙也共有50钱.问甲、乙各带了多少钱？设甲原有 $ x $ 钱，乙原有 $ y $ 钱，可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}, {"id": "564581119414280192", "title": "吉林省吉林市吉化第九中学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318162977726464", "questionArticle": "<p>10．如图，在长方形 $ ABCD $ 中，放入六个形状、大小相同的小长方形（即空白的长方形）.若 $ AB=16\\mathrm{c}\\mathrm{m} $ ， $ EF=4\\mathrm{c}\\mathrm{m} $ ，则一个小长方形的面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/11/2/1/0/0/0/544318141133791232/images/img_1.jpg\" style=\"vertical-align:middle;\" width=\"110\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 16{\\mathrm{c}\\mathrm{m}}^{2} $ B． $ 21{\\mathrm{c}\\mathrm{m}}^{2} $ C． $ 24{\\mathrm{c}\\mathrm{m}}^{2} $ D． $ 32{\\mathrm{c}\\mathrm{m}}^{2} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 201, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 201, "timestamp": "2025-07-01T02:24:36.049Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}