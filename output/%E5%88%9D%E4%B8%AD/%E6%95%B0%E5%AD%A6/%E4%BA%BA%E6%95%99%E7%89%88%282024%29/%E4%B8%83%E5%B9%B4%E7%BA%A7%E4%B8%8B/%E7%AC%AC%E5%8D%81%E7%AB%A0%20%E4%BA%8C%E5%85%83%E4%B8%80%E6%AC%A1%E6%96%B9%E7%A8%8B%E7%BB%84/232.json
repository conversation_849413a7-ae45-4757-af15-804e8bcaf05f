{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 231, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "527899764307828736", "questionArticle": "<p>1．解方程组：</p><p>(1) $ \\begin{cases} 2x+4y=9 \\\\ x=2-y \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x+5 } { 3 }-\\dfrac { y } { 2 }=1 \\\\ y+3x=5 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-31", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899758737793024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527899758737793024", "title": "陕西省西安高新第一中学2024−2025学年上学期第二次月考八年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526893117896695808", "questionArticle": "<p>2．甘肃博物馆的“砂锅娃娃”系列文创备受欢迎，一个“素砂锅”中含有3个“戏精豆芽”和2个“弹弹粉条”，一名工作人员1天能缝制180个“戏精豆芽”或者240个“弹弹粉条”，若博物馆有15名工作人员缝制“戏精豆芽”和“弹弹粉条”，为了使每天缝制的两种娃娃刚好配套，假设<i>x</i>名工作人员缝制“戏精豆芽”，<i>y</i>名工作人员缝制“弹弹粉条”，根据题意列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=15 \\\\ 180x\\times 2=240y\\times 3 \\end{cases}  $ B． $ \\begin{cases} x+y=15 \\\\ 180x\\times 3=240y\\times 2 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=15 \\\\ 2x=3y \\end{cases}  $ D． $ \\begin{cases} x+y=15 \\\\ 3x=2y \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆南开 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-31", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516371766903611392", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "516371766903611392", "title": "重庆市南开中学校2024−2025学年八年级上学期11月期中数学试题", "paperCategory": 1}, {"id": "526893111806566400", "title": "辽宁省沈阳市南昌中学2024−2025学年八年级上学期12月数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527899274782220288", "questionArticle": "<p>3．若 $ \\begin{cases} x=2 \\\\ y=-3 \\end{cases}  $ 是关于<i>x</i>和<i>y</i>的二元一次方程 $ kx-2y=4 $ 的解，则<i>k</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西山西大学附中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-31", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527899268750811136", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527899268750811136", "title": "山西省太原市小店区山西大学附属中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "527583879462428672", "title": "辽宁省沈阳市第一八四中学2024−2025学年八年级上学期数学期末复习训练卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527899762399420416", "questionArticle": "<p>4．幻方是古老的数学问题，我国古代的《洛书》中记载了最早的幻方——九宫格．将9个数填入幻方的空格中，要求每一横行、每一竖列以及两条对角线上的3个数之和相等，例如图（1）就是一个幻方．图（2）是一个未完成的幻方，则 $ x $ 与 $ y $ 的和是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/27/2/1/0/0/0/527899647936864259/images/img_7.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\"></p><p>A．9B．10C．11D．12</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2022湖北武汉 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 18, "referenceNum": 2, "createTime": "2024-12-31", "keyPointIds": "16267|16311|16433", "keyPointNames": "有理数的加法运算律|规律型：数与式的变化类|数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207093762228527104", "questionFeatureName": "规律探究题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "207093762228527104", "title": "湖北省武汉市2022年中考数学真题", "paperCategory": 1}, {"id": "527899758737793024", "title": "陕西省西安高新第一中学2024−2025学年上学期第二次月考八年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527900892143591424", "questionArticle": "<p>5．重庆火锅深受全国游客的的喜爱，其中毛肚和鸭肠是最畅销的两款菜品，某网红火锅店2份毛肚和3份鸭肠共166元;4份毛肚和5份鸭肠共302元．</p><p>(1)求毛肚和鸭肠的单价；</p><p>(2)元旦将至，火锅店的食材进价上涨了，其中某网红菜品的每份进价上涨了 $ 20\\% $ ，涨价后花1500元进货该菜品的份数比涨价前花同样的钱进货的该菜品份数少了10份，求该网红菜品涨价前的每份进价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆八中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-30", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527900882253422592", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527900882253422592", "title": "重庆市第八中学校2024—2025学年九年级上学期第三学月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527584038208446464", "questionArticle": "<p>6．解下列方程组：</p><p>(1) $ \\begin{cases} y=2x+1 \\\\ 3x-2y=2 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 2x-y=5 \\\\ 4x+3y=-10 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东济外 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-29", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527584030084079616", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527584030084079616", "title": "山东省济南外国语学校2024−2025学年上学期12月份月考八年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526893119817687040", "questionArticle": "<p>7．列二元一次方程组解应用题：</p><p>爸爸骑摩托车带着小明在公路上匀速行驶，小明每隔一段时间看到的里程表上的数如下：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.9pt;\"><p style=\"text-align:center;\">时刻</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 98.2pt;\"><p style=\"text-align:center;\"> $ 9:00 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 150.35pt;\"><p style=\"text-align:center;\"> $ 10:00 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 134.9pt;\"><p style=\"text-align:center;\"> $ 11:30 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.9pt;\"><p style=\"text-align:center;\">里程表上的数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 98.2pt;\"><p style=\"text-align:center;\">是一个两位数，它的两个数字之和是6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 150.35pt;\"><p style=\"text-align:center;\">是一个两位数，它的十位与个位数字与 $ 9:00 $ 所看到的正好互换了</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 134.9pt;\"><p style=\"text-align:center;\">是一个三位数，它比 $ {\\rm 9} 9:00 $ 时看到的两位数中间多了个0</p></td></tr></table><p>设： $ 9:00 $ 时里程碑上的这个两位数十位数字为<i>x</i>，个位数字为<i>y</i>，回答下列问题：</p><p>(1)用含<i>x</i>，<i>y</i>代数式表示： $ 9:00 $ 时里程碑上的数字<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>； $ 10:00 $ 时看到里程表上的数<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>； $ 11:30 $ 时看到里程表上的数<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)列方程组并求出 $ 10:00 $ 时里程碑上的数．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024辽宁沈阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-29", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526893111806566400", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "526893111806566400", "title": "辽宁省沈阳市南昌中学2024−2025学年八年级上学期12月数学试卷", "paperCategory": 1}, {"id": "527583879462428672", "title": "辽宁省沈阳市第一八四中学2024−2025学年八年级上学期数学期末复习训练卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527583887456772096", "questionArticle": "<p>8．（1）计算： $ 2\\sqrt { 2 }+\\sqrt { 2 }-\\sqrt[3] { 8 } $ ；</p><p>（2）解方程组： $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} 2x+y=3 \\\\ 3x+y=4 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2024-12-29", "keyPointIds": "16290|16299|16424", "keyPointNames": "立方根|实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527583879462428672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527583879462428672", "title": "辽宁省沈阳市第一八四中学2024−2025学年八年级上学期数学期末复习训练卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527583884948578304", "questionArticle": "<p>9．用绳子量井深：把绳子三折来量，井外余4尺；把绳子四折来量，井外余1尺，则井深和绳长分别是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．8尺，36尺B．3尺，13尺C．10尺，34尺D．11尺，37尺</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁沈阳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-29", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527583879462428672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527583879462428672", "title": "辽宁省沈阳市第一八四中学2024−2025学年八年级上学期数学期末复习训练卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527583387030167552", "questionArticle": "<p>10．（1）计算： $ {\\left( { \\sqrt { 2023 }-1 } \\right) ^ {0}}-\\left  | { \\sqrt { 3 }-2 } \\right  | +{\\left( { \\dfrac { 1 } { 5 } } \\right) ^ {-1}} $ ；</p><p>（2）解方程组： $ \\begin{cases} 2x+y=4 \\\\ 2y+1=5x \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江西吉安 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-29", "keyPointIds": "16299|16323|16372|16424", "keyPointNames": "实数的运算|零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527583381254610944", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527583381254610944", "title": "江西省吉安市2024−2025学年八年级上学期12月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 232, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 232, "timestamp": "2025-07-01T02:28:11.441Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}