{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 209, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "536617058592661504", "questionArticle": "<p>1．综合与实践：</p><p>【问题情境】</p><p>2024年3月4日，“定山西・向未来”城市智趣跑活动在山西太原开幕．本次活动，激扬全民运动热情．活动期间，小明所在的班级开展知识竞赛，需要去商店购买 $ \\mathrm{ A } $ ， $ B $ 两种款式的运动盲盒作为奖品．</p><p>素材 $ 1 $ ：某商店在无促销活动时，若买 $ 15 $ 个 $ \\mathrm{ A } $ 款运动盲盒、 $ 10 $ 个 $ B $ 款运动盲盒，共需 $ 230 $ 元；若买 $ 25 $ 个 $ \\mathrm{ A } $ 款运动盲盒、 $ 25 $ 个 $ B $ 款运动盲盒，共需 $ 450 $ 元．</p><p>素材2：该商店开展促销活动：用 $ 35 $ 元购买会员卡成为会员后，凭会员卡购买商店内任何商品，一律按商品价格的 $ 8 $ 折出售 $ ( $ 已知小明在此之前不是该商店的会员 $ ) $ ；线上淘宝店促销活动：购买商店内任何商品，一律按商品价格的 $ 9 $ 折出售且包邮．</p><p>【解决问题】</p><p>(1)该商店在无促销活动时，求 $ \\mathrm{ A } $ 款运动盲盒和 $ B $ 款运动盲盒的销售单价各是多少元？</p><p>【拓展提升】</p><p>(2)小明计划在促销期间购买 $ \\mathrm{ A } $ ， $ B $ 两款运动盲盒共40个，其中 $ \\mathrm{ A } $ 款运动盲盒 $ m $ 个( $ 0  &lt;  m  &lt;  40 $ )，若在线下商店成为会员购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；若在线上淘宝店购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．(均用含 $ m $ 的代数式表示)</p><p>【综合应用】</p><p>(3)请你帮小明算一算，在(2)的条件下，购买 $ \\mathrm{ A } $ 款运动盲盒的数量在什么范围内时，线下购买方式更合算？</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/20/2/1/0/0/0/536617005094313985/images/img_26.png\" style=\"vertical-align:middle;\" width=\"171\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳实验学校 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-31", "keyPointIds": "16304|16437|16486", "keyPointNames": "列代数式|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617049658793984", "questionFeatureName": "综合与实践题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536617049658793984", "title": "广东省深圳实验学校初中部2024−2025学年上学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "536617056411623424", "questionArticle": "<p>2．（1）计算： $ \\sqrt { 50 }\\times \\sqrt { 8 }-\\dfrac { \\sqrt { 6 }\\times \\sqrt { 3 } } { 2 } $ ；</p><p>（2）解方程组： $ \\begin{cases} 3x+2y=8 \\\\ 5x-y=9 \\end{cases}  $ ；</p><p>（3）解不等式组： $ \\begin{cases} \\dfrac { 2x-1 } { 3 }\\geqslant  -1 \\\\ 5-2x &gt; 2-x \\end{cases}  $ ，并将解集在数轴上表示出来．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/20/2/1/0/0/0/536617005081731072/images/img_14.png\" style=\"vertical-align:middle;\" width=\"338\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳实验学校 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-31", "keyPointIds": "16389|16424|16489", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617049658793984", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536617049658793984", "title": "广东省深圳实验学校初中部2024−2025学年上学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537018642602434560", "questionArticle": "<p>3．中国新能源汽车正处在快速发展阶段，产销量和出口量均居世界第一，某汽车销售公司针对市场情况，计划购进一批新能源汽车进行销售，据了解购进 $ 1 $ 辆 $ \\mathrm{ A } $ 型和 $ 3 $ 辆 $ B $ 型汽车需要 $ 75 $ 万元， $ 3 $ 辆 $ \\mathrm{ A } $ 型和 $ 2 $ 辆 $ B $ 型汽车需要 $ 85 $ 万元．</p><p>(1)求 $ A、B $ 两种型号的汽车每辆的进价各是多少万元？</p><p>(2)该公司准备用正好 $ 205 $ 万元购进这两种型号的汽车，请你帮助该公司设计部门，写出有哪几种购买方案．</p><p>(3)若销售 $ A、B $ 两种型号的汽车每辆分别可获得利润 $ 1 $ 万元和 $ 1.2 $ 万元，在（2）方案中如果全部售出，哪种方案获利最大？最大利润是多少万元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽淮北 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-31", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018634234798080", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537018634234798080", "title": "安徽省淮北市五校联考2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537018707278602240", "questionArticle": "<p>4．“九宫图”传说是远古时代洛河中的一个神龟背上的图案，故又称“龟背图”，中国古代数学史上经常研究这一神话．数学上的“九宫图”所体现的是一个 $ 3\\times 3 $ 表格，一行的三个数，列的三个数，斜对角的三个数之和都相等，也称为三阶幻方，如图是一个满足条件的三阶幻方的一部分，则 $ x+y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/21/2/1/0/0/0/537018680355364864/images/img_11.png\" style=\"vertical-align:middle;\" width=\"103\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽淮南 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-31", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018697166135296", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537018697166135296", "title": "安徽省淮南市2024−2025学年七年级上学期1月期末质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537018958974590976", "questionArticle": "<p>5．我市的雨山湖公园，娟秀妩媚，环境优雅，湖水清澈见底，是市民游玩休闲的好地方．某校七年级1班学生计划假期去雨山湖游玩，游船价格如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>船型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>四座电动船</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>六座电动船</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p> $ 120 $ 元/小时</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p> $ 140 $ 元/小时</p></td></tr></table><p>已知所有学生均有座位且坐船游玩 $ 1 $ 小时，请解决下面问题：</p><p>(1)若租用四座电动船条数与六座电动船条数之比为 $ 3：2 $ ，所有船恰好坐满，需花费 $ 1280 $ 元，那么租用了几条四座电动船？</p><p>(2)若每条船均坐满，且每种船型至少一条；列举出所有可行的租船方案，并计算出每种方案的价格，指出最省钱的方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽马鞍山 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-30", "keyPointIds": "16412|16420", "keyPointNames": "和差倍分问题|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018947784187904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537018947784187904", "title": "安徽省马鞍山市第七中学2024—2025学年上学期期末考试七年级数学测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537018957980540928", "questionArticle": "<p>6．（1）解方程： $ \\dfrac { x+2 } { 2 }-\\dfrac { 2x-1 } { 3 }=1 $ </p><p>（2）在等式 $ y=ax{^{2}}+bx $ 中，当 $ x=1 $ 时， $ y=5 $ ； $ x=-1 $ 时， $ y=-1 $ ．求<i>a</i>和<i>b</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽马鞍山 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-30", "keyPointIds": "16305|16402|16424", "keyPointNames": "代数式求值|解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018947784187904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537018947784187904", "title": "安徽省马鞍山市第七中学2024—2025学年上学期期末考试七年级数学测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537018957313646592", "questionArticle": "<p>7．已知关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x+4y=14 \\\\ x-y=1-2k \\end{cases}  $ 有正整数解，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽马鞍山 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-30", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018947784187904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537018947784187904", "title": "安徽省马鞍山市第七中学2024—2025学年上学期期末考试七年级数学测试", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537018954604126208", "questionArticle": "<p>8．甲、乙两人分别在<i>A</i>、<i>B</i>两地，以各自的速度同时出发．如果相向而行，两人 $ { { 0 } }{ { . } }{ { 5 } }{ \\rm{ h } } $ 后相遇；如果同向而行，两人 $ { { 2 } }{ \\rm{ h } } $ 后相遇；问甲从<i>A</i>地到<i>B</i>地需要（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;） $ { \\rm{ h } } $ ．</p><p>A． $ \\dfrac { 4 } { 5 } $ B． $ \\dfrac { 4 } { 3 } $ C． $ \\dfrac { 4 } { 5 } $ 或 $ \\dfrac { 4 } { 3 } $ D． $ \\dfrac { 5 } { 4 } $ 或 $ \\dfrac { 3 } { 4 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽马鞍山 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-30", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018947784187904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537018947784187904", "title": "安徽省马鞍山市第七中学2024—2025学年上学期期末考试七年级数学测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537019130815225856", "questionArticle": "<p>9．根据以下素材，探索完成任务一：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>如何设计购买方案？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材 $ 1 $ </p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>某校 $ 40 $ 名同学要去参观航天展览馆，已知展览馆分为 $ \\mathrm{ A } $ ， $ B $ ， $ C $ 三个场馆，且购买 $ 1 $ 张 $ \\mathrm{ A } $ 场馆门票和 $ 1 $ 张 $ B $ 场馆门票共需 $ 90 $ 元，购买 $ 3 $ 张 $ \\mathrm{ A } $ 场馆门票和 $ 2 $ 张 $ B $ 场馆门票共需 $ 230 $ 元 $ {\\rm ．} C $ 场馆门票为每张 $ 15 $ 元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材 $ 2 $ </p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>由于场地原因，要求到 $ \\mathrm{ A } $ 场馆参观的人数要少于到<i>B</i>场馆参观的人数，且每位同学只能选择一个场馆参观．参观当天刚好有优惠活动：每购买 $ 1 $ 张 $ \\mathrm{ A } $ 场馆门票就赠送 $ 1 $ 张 $ C $ 场馆门票．</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务 $ 1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>确定场馆门票价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>求 $ \\mathrm{ A } $ 场馆和 $ B $ 场馆的门票价格．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务 $ 2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>探究经费的使用</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若购买 $ \\mathrm{ A } $ 场馆门票赠送的 $ C $ 场馆门票刚好够参观 $ C $ 场馆的同学使用，求此次购买门票所需总金额的最小值．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务 $ 3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>拟定购买方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若参观 $ C $ 场馆的同学除了使用掉赠送的门票外，还需购买部分门票，且让去 $ \\mathrm{ A } $ 场馆的人数尽量的多，最终购买三种门票共花费了 $ 1100 $ 元，请你直接写出购买方案．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-30", "keyPointIds": "16426|16438|16535", "keyPointNames": "二元一次方程组的应用|和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537019123483582464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537019123483582464", "title": "广东省深圳市罗湖区深圳中学2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537019619011239936", "questionArticle": "<p>10．【材料1】如果一个自然数 $ a $ 是某一个整数 $ b $ 的平方，即 $ a=b{^{2}} $ ，那么这个自然数 $ a $ 叫做完全平方数．例如0，1，4，9，16，25，36，…，都是完全平方数，其部分性质如下：</p><p>①完全平方数的个位数字只能是0，1，4，5，6，9．</p><p>②任何偶数的平方一定能被4整除；任何奇数的平方被4（或8）除余1，即被4除余2或3的数一定不是完全平方数．</p><p>③完全平方数的个位数字是奇数时，其十位上的数字必为偶数．完全平方数的个位数字是6时，其十位数字必为奇数．…</p><p>【材料2】平方差公式： $ x{^{2}}-y{^{2}}=\\left ( { x+y } \\right ) \\left ( { x-y } \\right )  $ ．</p><p>(1)根据以上材料，判断下列这些数是否为完全平方数（填“是”或“否”）</p><p>①121（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）；② $ \\dfrac { 9 } { 16 } $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）；③1256341386（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>(2)已知 $ m=9{^{2}}+9{^{2}}\\times 10{^{2}}+10{^{2}} $ ，求证： $ m $ 是完全平方数；</p><p>(3)已知自然数 $ n $ 使得 $ n{^{2}}-75 $ 为完全平方数，求 $ n $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙青竹湖湘一外国语学校 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-28", "keyPointIds": "16332|16344|16424", "keyPointNames": "完全平方公式|运用平方差公式分解因式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537019605560107008", "questionFeatureName": "阅读材料题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537019605560107008", "title": "湖南省长沙市开福区长青竹湖湘一外国语学校2024−2025学年八年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 210, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 210, "timestamp": "2025-07-01T02:25:35.945Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}