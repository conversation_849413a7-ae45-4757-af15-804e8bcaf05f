{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 234, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "527584036094517248", "questionArticle": "<p>1．在新年来临之际，梅梅打算去花店为妈妈挑选新年礼物．已知康乃馨每枝6元，百合每枝5元．梅梅购买这两种花18枝恰好用去100元，设她购买 $ x $ 枝康乃馨， $ y $ 枝百合，可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 6x+5y=100 \\\\ x+y=18 \\end{cases}  $　　　　B． $ \\begin{cases} 5x+6y=100 \\\\ x+y=18 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=100 \\\\ 6x+5y=18 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=100 \\\\ 5x+6y=18 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东济外 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-27", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527584030084079616", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527584030084079616", "title": "山东省济南外国语学校2024−2025学年上学期12月份月考八年级数学试题", "paperCategory": 1}, {"id": "404900646900506624", "title": "广东省深圳市宝安区2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526057372009144320", "questionArticle": "<p>2．某文具店购进<i>A</i>，<i>B</i>两种型号的笔袋，两次购进笔袋的情况如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>进货批次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.25pt;\"><p>A型笔袋（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.65pt;\"><p>B型笔袋（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>总费用（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>一</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.25pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.65pt;\"><p>50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>4000</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>二</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.25pt;\"><p>50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.65pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>3500</p></td></tr></table><p>(1)求<i>A</i>，<i>B</i>两种型号的笔袋进价各是多少元？</p><p>(2)在销售过程中，为了增大<i>A</i>型笔袋的销售量，超市决定对<i>A</i>型笔袋进行降价销售，当销售单价为40元时，每天可以售出20个，每降价1元，每天将多售出5个，请问超市将每个<i>A</i>型笔袋降价多少元时，每天售出<i>A</i>型笔袋的利润为240元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-27", "keyPointIds": "16438|16463", "keyPointNames": "和差倍分问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526057362454519808", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "526057362454519808", "title": "四川省达州市渠县中学2024 ~ 2025 学年数学九年级上学期期末数学模拟测试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526895599737675776", "questionArticle": "<p>3．如图， $ \\vartriangle ABC $ 的内切圆 $ \\odot O $ 与 $ BC $ ， $ CA $ ， $ AB $ 分别相切于点<i>D</i>、<i>E</i>、<i>F</i>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/28/2/20/0/0/0/528184882297610241/images/img_1.png\" style='vertical-align:middle;' width=\"227\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)若 $ \\angle ABC=50{}\\degree  $ ， $ \\angle ACB=75{}\\degree  $ ，求 $ ∠BOC $ 的度数；</p><p>(2)若 $ AB=13 $ ， $ BC=11 $ ， $ AC=10 $ ，求 $ AE $ 的长．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024天津蓟州一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-25", "keyPointIds": "16443|16732|16733", "keyPointNames": "解三元一次方程组|切线长定理|三角形内切圆", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526895590879305728", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526895590879305728", "title": "天津市蓟州区第一中学2024−2025学年九年级上学期第二次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526895929753903104", "questionArticle": "<p>4．若一个四位正整数 $   {\\overline{ abcd }}   $ 满足： $ a+c=b+d $ ，我们就称该数是“振兴数”，则最小的“振兴数”是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；若一个“振兴数”<i>m</i>满足千位数字与百位数字的平方差是15，且十位数字与个位数的和能被5整除．则满足条件的“振兴数”<i>m</i>的最小值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-25", "keyPointIds": "16424|29412", "keyPointNames": "加减消元法解二元一次方程组|数的整除", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526895922300624896", "questionFeatureName": "新定义问题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526895922300624896", "title": "重庆市为明学校2024−2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}, {"id": "408039633165000704", "title": "重庆市綦江区2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "526895927296040960", "questionArticle": "<p>5．《九章算术》中有这样一道题：“今有善行者一百步，不善行者六十步．今不善行者先行一百步，善行者追之，问几何步及之？”意思是：走路快的人走100步时，走路慢的人只走60步，走路慢的人先走100步，走路快的人要走多少步才能追上？设走路快的人走<i>x</i>步才能追上走路慢的人，此时走路慢的人走了<i>y</i>步，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=y+100 \\\\ \\dfrac { x } { 100 }=\\dfrac { y } { 60 } \\end{cases}  $　　　　B． $ \\begin{cases} x=y+100 \\\\ \\dfrac { x } { 60 }=\\dfrac { y } { 100 } \\end{cases}  $</p><p>C． $ \\begin{cases} x=y-100 \\\\ \\dfrac { x } { 100 }=\\dfrac { y } { 60 } \\end{cases}  $　　　　D． $ \\begin{cases} x=y-100 \\\\ \\dfrac { x } { 60 }=\\dfrac { y } { 100 } \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|450000|440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024重庆 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 3, "createTime": "2024-12-25", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526895922300624896", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526895922300624896", "title": "重庆市为明学校2024−2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}, {"id": "430136029540032512", "title": "2023年广西河池市宜州区部分校联考中考一模数学试题", "paperCategory": 1}, {"id": "402645418461405184", "title": "广东省深圳市龙华区2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "526780736625483776", "questionArticle": "<p>6．列方程解应用问题：</p><p>某学校购买了排球和足球共138个，共花了5400元．其中排球每个30元，足球每个50元，问排球和足球各买了多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024北京北京市十一学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2024-12-24", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526780727985217536", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "526780727985217536", "title": "北京市十一学校2024−2025学年七年级上学期期中数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "526530230804062208", "questionArticle": "7．<table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">背景</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>【竞飞“低空经济第一城”】打开手机外卖软件下单，最快仅用时10分钟，便有无人机将奶茶、汉堡等商品“空投”到指定地点，这是记者日前在深圳中心公园亲身体验到的一幕．从理想照进现实，低空经济如今从概念逐渐落地，成为城市新质生产力的一部分，助力深圳竞飞“低空经济第一城”．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>某商店在无促销活动时，若买5件<i>A</i>商品，8件<i>B</i>商品，共需要2400元；若买8件<i>A</i>商品，5件<i>B</i>商品，共需2280元．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/25/2/20/0/0/0/527102502434545665/images/img_1.png\" style='vertical-align:middle;' width=\"110\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>该商店为了鼓励消费者使用无人机配送服务，开展促销活动：</p><p>①若消费者用250元购买无人机配送服务卡，商品一律按标价的七五折出售；</p><p>②若消费者不使用无人机配送服务：凡购买店内任何商品，一律按照标价的八折出售．</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>在该商店在无促销活动时，求<i>A</i>，<i>B</i>商品的销售单价分别是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>某南山科技公司计划在促销期间购买<i>A</i>，<i>B</i>两款商品共30件，其中<i>A</i>商品购买<i>a</i>件（ $ 0＜a＜30 $ ）；</p><p>①若使用无人机配送商品，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>②若不使用无人机配送商品，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．（结果均用含<i>a</i>的代数式表示）；</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务3</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>请你帮该科技公司算一算，在任务2的条件下，购买<i>A</i>产品的数量在什么范围内时，使用无人机配送商品更合算？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖南株洲 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-24", "keyPointIds": "16304|16437|16486", "keyPointNames": "列代数式|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526530221031333888", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526530221031333888", "title": "湖南省株洲市景弘中学2024—2025学年上学期八年级数学第一次月考试卷", "paperCategory": 1}, {"id": "509849816983183360", "title": "浙江省宁波市 四校（鄞州实验、东钱湖、曙光、海三外）2024−2025学年上学期联考八年级数学期中测试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524199365998583808", "questionArticle": "<p>8．已知关于 $ x $ 和 $ y $ 的方程组 $ \\begin{cases} ax+by=78 \\\\ cx-6y=-21 \\end{cases}  $ 的解是 $ \\begin{cases} x=2.7 \\\\ y=6.1 \\end{cases}  $ ，则另一关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} a\\left ( { x+2 } \\right ) +b\\left ( { y-3 } \\right ) =78 \\\\ c\\left ( { x+2 } \\right ) -6\\left ( { y-3 } \\right ) =-21 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东日照 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-23", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524199358406893568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524199358406893568", "title": "山东省日照市东港区新营中学2023−2024学年九年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524199707901468672", "questionArticle": "<p>9．下面是小林同学解方程组 $ \\begin{cases} 2x+y=5 \\\\ x-3y=6 \\end{cases}  $ 的过程，请认真阅读并完成相应任务．</p><p>解： $ \\begin{cases} 2x+y=5① \\\\ x-3y=6② \\end{cases}  $ ，</p><p>由①得 $ y=5-2x $ ③，第一步</p><p>把③代入②，得 $ x-3(5-2x)=6 $ ，第二步</p><p>整理得 $ x-15-6x=6 $ ，第三步</p><p>解得 $ -5x=21 $ ，即 $ x=-\\dfrac { 21 } { 5 } $ ．第四步</p><p>把 $ x=-\\dfrac { 21 } { 5 } $ 代入③，得 $ y=\\dfrac { 67 } { 5 } $ ，</p><p>则方程组的解为 $ \\begin{cases} x=-\\dfrac { 21 } { 5 } \\\\ y=\\dfrac { 67 } { 5 } \\end{cases}  $ 第五步</p><p>任务一：</p><p>①以上求解过程中，小林用了<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>消元法．（填“代入”或“加减”）</p><p>②第<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>步开始出现错误，这一步错误的原因是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>任务二：该方程组的正确解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>任务三：请你根据平时的学习经验，就解二元一次方程组时还需要注意的事项给其他同学提一点建议．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西运城 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2024-12-23", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524199700175560704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524199700175560704", "title": "山西省运城市盐湖区实验中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524199708497059840", "questionArticle": "<p>10．某商店出售普通练习本和精装练习本，150本普通练习本和100本精装练习本的销售总额为1450元，200本普通练习本和50本精装练习本销售总额为1100元．</p><p>(1)求普通练习本和精装练习本的销售单价分别是多少？</p><p>(2)该商店计划再次购进500本练习本，已知普通练习本的进价为2元/个，精装练习本的进价为7元/个，设购买普通练习本<i>x</i>本，求所获利润<i>W</i>关于<i>x</i>的函数关系式．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西运城 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2024-12-23", "keyPointIds": "16424|16438|16536", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|列一次函数解析式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524199700175560704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524199700175560704", "title": "山西省运城市盐湖区实验中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "402647767561052160", "title": "辽宁省沈阳市沈北新区2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 235, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 235, "timestamp": "2025-07-01T02:28:33.707Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}