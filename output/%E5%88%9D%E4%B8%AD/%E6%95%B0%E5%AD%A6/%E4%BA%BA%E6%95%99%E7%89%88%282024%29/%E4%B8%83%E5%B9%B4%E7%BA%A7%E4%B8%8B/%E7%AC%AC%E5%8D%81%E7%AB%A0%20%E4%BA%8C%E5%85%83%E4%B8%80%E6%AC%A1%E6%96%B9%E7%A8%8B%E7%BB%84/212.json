{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 211, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "537020875188510720", "questionArticle": "<p>1．我国古代数学著作《孙子算经》有“多人共车”问题：“今有三人共车，二车空；二人共车，九人步，问：人与车各几何？”其大意如下：有若干人要坐车，如果每3人坐一辆车，那么有2辆空车；如果每2人坐一辆车，那么有9人需要步行，问人与车各多少？若设有 $ x $ 人，有 $ y $ 辆车，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3\\left ( { y-2 } \\right ) =x \\\\ 2y=x-9 \\end{cases}  $ B． $ \\begin{cases} 3y-2=x \\\\ 2y-x=9 \\end{cases}  $ </p><p>C． $ \\begin{cases} 3\\left ( { y-2 } \\right ) =x \\\\ 2y-9=x \\end{cases}  $ D． $ \\begin{cases} 3\\left ( { y-2 } \\right ) =x \\\\ 2y=x+9 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西陕西师范大学附属中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537020870025322496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537020870025322496", "title": "陕西省西安市陕西师范大学附属中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537020798965424128", "questionArticle": "<p>2．“灯笼高高照，春联对对妙”，挂灯笼、贴春联是春节的传统习俗．临近春节，全都超市计划购进春联和灯笼这两种商品销售．据了解1个灯笼和2副春联的进价共计60元；5个灯笼和3副春联的进价共计195元．</p><p>(1)每个灯笼和每副春联的进价各是多少元？</p><p>(2)如果该超市计划购进10个灯笼和40副春联，准备1000元够吗？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安市第八十三中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16437|28548", "keyPointNames": "销售利润问题|有理数混合运算的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537020789465325568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537020789465325568", "title": "陕西省西安市八十三中学2024−2025学年上学期期末学业水平测试八年级数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537020797577109504", "questionArticle": "<p>3．解方程组： $ \\begin{cases} 3x+y=1 \\\\ x-2y=-9 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025陕西西安市第八十三中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537020789465325568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537020789465325568", "title": "陕西省西安市八十三中学2024−2025学年上学期期末学业水平测试八年级数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537437859017433088", "questionArticle": "<p>4．如图，某化工厂与<i>A</i>，<i>B</i>两地有公路和铁路相连．这家工厂从<i>A</i>地购买每吨1000元的原料运回工厂，制成每吨8000元的产品运到<i>B</i>地．已知公路的运价为1.5元／（吨 $ ·{ \\rm{ k } }{ \\rm{ m } } $ ），铁路的运价为1.0元／（吨 $ ·{ \\rm{ k } }{ \\rm{ m } } $ ）．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/05/2/20/0/0/0/542322051077414913/images/img_1.png\" style='vertical-align:middle;' width=\"236\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)从<i>A</i>地运回<i>m</i>吨原材料到工厂，需要的运费是多少？（用含<i>m</i>的代数式表示）</p><p>(2)若其中一批原料，从<i>A</i>地运回，到生产成产品运到<i>B</i>地，两次运输共支出公路运费16500元，铁路运费93000元．这一批原料是多少吨？每吨原料能加工成的产品数量是多少？</p><p>(3)若生产该产品，每月的其它成本费为350000元，每吨的生产费为3000元，求该产品每月的毛利润<i>w</i>与原料<i>x</i>吨之间的函数关系．（规定：每月的毛利润=销售额-原料费-其它成本费-生产费-运输费）</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆南岸 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16304|16437|16536", "keyPointNames": "列代数式|销售利润问题|列一次函数解析式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537437845406916608", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "537437845406916608", "title": "重庆市南岸区2024—2025学年八年级上学期期末质量监测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537436970533822464", "questionArticle": "<p>5．对于多项式 $ kx+b $ （其中 $ k $ ， $ b $ 为常数），若 $ x $ 分别用 $ 3 $ ， $ -1 $ 代入时， $ kx+b $ 的值分别为 $ -7 {\\rm ，5} $ ，则 $ k+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537436963659358208", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537436963659358208", "title": "湖南省怀化市2024−2025学年七年级上学期数学期末抽测卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537436972710666240", "questionArticle": "<p>6．一个圆柱形容器中，现有20个单位高度的水．请根据图中给出的信息，解答下列问题：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/23/2/1/0/0/0/537436940892676096/images/img_17.png\" style=\"vertical-align:middle;\" width=\"384\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)如果放入6个球，使水面上升到40个单位的高度，放入的大球、小球各多少个？</p><p>(2)现放入若干个（1）中的大球或小球，使得容器恰好装满，问有几种可能？请写出过程，并一一列出．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537436963659358208", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537436963659358208", "title": "湖南省怀化市2024−2025学年七年级上学期数学期末抽测卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537436971590787072", "questionArticle": "<p>7．解方程（组）：</p><p>(1) $ 2\\left ( { 2x-3 } \\right ) +2=3x-1 $ ．</p><p>(2) $ \\begin{cases} -2x+3y=0① \\\\ 6x-2y=14② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537436963659358208", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537436963659358208", "title": "湖南省怀化市2024−2025学年七年级上学期数学期末抽测卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537437852453347328", "questionArticle": "<p>8．下列4组数据中，是二元一次方程 $ 2x+y=5 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=0 \\\\ y=2 \\end{cases}  $</p><p>B． $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $</p><p>C． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $</p><p>D． $ \\begin{cases} x=-2 \\\\ y=-1 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025重庆南岸 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537437845406916608", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "537437845406916608", "title": "重庆市南岸区2024—2025学年八年级上学期期末质量监测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537437193800818688", "questionArticle": "<p>9．为了激励更多的学生热爱体育运动，某校购买了若干副乒乓球拍和羽毛球拍，对表现优异的学生进行奖励．若每副羽毛球拍比每副乒乓球拍贵 $ 30 $ 元，且购买 $ 3 $ 副乒乓球拍和 $ 2 $ 副羽毛球拍共需 $ 435 $ 元．求 $ 1 $ 副乒乓球拍和 $ 1 $ 副羽毛球拍各是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西陕西师范大学附属中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537437184955031552", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "537437184955031552", "title": "陕西省西安市陕西师范大学附属中学2024-−2025学年九年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536617980832030720", "questionArticle": "<p>10．二元一次方程组 $ \\begin{cases} 3x-2y=3 \\\\ x+2y=5 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁沈阳 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536617974691569664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "536617974691569664", "title": "辽宁省沈阳市浑南区东北育才学校2024−2025学年九年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}]}}, "requestData": {"pageNum": 212, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 212, "timestamp": "2025-07-01T02:25:48.824Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}