{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 208, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "537734167649361920", "questionArticle": "<p>1．已知方程组 $ \\begin{cases} 3x+2y=15 \\\\ 2x+3y=3k-1 \\end{cases}  $ 的解满足 $ x+y=4 $ ，则 $ k= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537734159936036864", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "537734159936036864", "title": "陕西省点 西安高新第一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537734165367660544", "questionArticle": "<p>2．已知关于 $ x,y $ 的方程组 $ \\begin{cases} y=3x-1 \\\\ y=mx+n \\end{cases}  $ 的解为 $ \\begin{cases} x=1 \\\\ y=a \\end{cases}  $ ，则直线 $ l{{}_{ 1 } }:y=3x-1 $ 与直线 $ l{{}_{ 2 } }:y=mx+n $ 的交点 $ P $ 的坐标为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ { {   } }\\left ( { 2,1 } \\right )  $　　　　B． $ { {   } }\\left ( { -2,1 } \\right )  $　　　　C． $ \\left ( { 1,2 } \\right )  $　　　　D． $ \\left ( { 1,-2 } \\right )  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16420|16540", "keyPointNames": "二元一次方程的解|一次函数与二元一次方程（组）", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537734159936036864", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "537734159936036864", "title": "陕西省点 西安高新第一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "536335168996417536", "questionArticle": "<p>3．为积极落实银川市委制定印发的《关于2023年度乡村振兴“一村一年一事”行动实施方案》，城建部门计划对某村一段长300米的道路进行改造，由甲，乙两个工程队先后接力完成，已知甲工程队每天改造15米，乙工程队每天改造10米．</p><p>(1)若这两个工程队共用时25天，求甲，乙两个工程队分别改造多少米．</p><p>根据题意，宁宁和夏夏两个同学分别列出了如下的方程组：</p><p>宁宁： $ \\begin{cases} x+y=25 \\\\ 15x+10y=300 \\end{cases}  $ ，解得 $ \\begin{cases} x=10 \\\\ y=15 \\end{cases}  $ ．</p><p>夏夏： $ \\begin{cases} x+y=300 \\\\ \\dfrac { x } { 15 }+\\dfrac { y } { 10 }=25 \\end{cases}  $ ，解得 $ \\begin{cases} x=150 \\\\ y=150 \\end{cases}  $ ．</p><p>宁宁所列方程组中的<i>x</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，<i>y</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>夏夏所列方程组中的<i>x</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，<i>y</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>(2)若甲工程队工作一天的费用是0.6万元，乙工程队工作一天的费用是0.8万元，要使总费用不超过18万元，则甲工程队至少工作多少天？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "640000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024宁夏银川 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-04", "keyPointIds": "16431|16486", "keyPointNames": "工程问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536335161811574784", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536335161811574784", "title": "2024年宁夏回族自治区银川外国语实验学校 中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "536335829280530432", "questionArticle": "<p>4．今有人合伙购物，每人出 $ 8 $ 钱，会多 $ 3 $ 钱；每人出 $ 7 $ 钱，又差 $ 4 $ 钱，问人数、物价各多少？设有 $ x $ 人，商品的价格为 $ y $ ，依题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x+3=y \\\\ 7x+4=y \\end{cases}  $ B． $ \\begin{cases} 8x-3=y \\\\ 7x-4=y \\end{cases}  $ C． $ \\begin{cases} 8x+3=y \\\\ 7x-4=y \\end{cases}  $ D． $ \\begin{cases} 8x-3=y \\\\ 7x+4=y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024四川成都 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-02-03", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536335823924404224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536335823924404224", "title": "2024年四川省成都市郫都区九年级中考数学一诊试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "536336410074193920", "questionArticle": "<p>5．若 $ x{{}_{ 1 } },x{{}_{ 2 } },x{{}_{ 3 } },…x{{}_{ n } } $ 中每一个数值只能取 $ 2 $ ， $ 0 $ ， $ -1 $ 中的一个，且 $ \\begin{cases} x{{}_{ 1 } }+x{{}_{ 2 } }+x{{}_{ 3 } }+\\ldots x{{}_{ n } }=-19 \\\\ x{{}_{ 1 } ^{ 2 }}+x{{}_{ 2 } ^{ 2 }}+x{{}_{ 3 } ^{ 2 }}+\\ldots x{{}_{ n } ^{ 2 }}=37 \\end{cases}  $ ，求 $ x{{}_{ 1 } ^{ 3 }}+x{{}_{ 2 } }{^{3}}+x{{}_{ 3 } }{^{3}}+\\ldots x{{}_{ n } }{^{3}} $ 的值<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川内江 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-03", "keyPointIds": "16305|16424", "keyPointNames": "代数式求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536336401350041600", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536336401350041600", "title": "2024年四川省内江市东兴区部分学校九年级一模考试数学模拟试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "536336592257982464", "questionArticle": "<p>6．（1）解不等式组： $ \\begin{cases} 7x+13\\geqslant  4\\left ( { x+1 } \\right )  \\\\ x-4  &lt;  \\dfrac { x-8 } { 3 } \\end{cases}  $ ；</p><p>（2）一艘轮船在相距90千米的甲、乙两地之间匀速航行，从甲地到乙地顺流航行用6小时，逆流航行比顺流航行多用4小时．求该轮船在静水中的速度和水流速度．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024新疆八一 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-02", "keyPointIds": "16430|16489", "keyPointNames": "行程问题|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536336584804704256", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "536336584804704256", "title": "2024年新疆维吾尔自治区乌鲁木齐八一中学中学数学中考一模试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536336681454051328", "questionArticle": "<p>7．某鲜花店出售甲、乙两种艺术花篮，八月份时，每个乙花篮的单价比甲花篮单价低20元，一个甲花篮与两个乙花篮的售价之和为260元．</p><p>(1)八月份，甲、乙两种艺术花篮的销售单价分别是多少元？</p><p>(2)据统计八月份甲、乙两种艺术花篮分别销售了40个和50个；九月份，随着国庆节的即将到来，顾客对艺术花篮的需求量增大，店主决定对甲种花篮进行降价促销，经市场调研，甲种花篮单价每降低1元，预计销量比八月份增加3个；乙种花篮销售单价不变，但其销量相比八月份也有所增加，预计增加的销量是甲种花篮增加销量的 $ \\dfrac { 1 } { 3 } $ ．若预计九月份甲、乙两种花篮的销售总额是11100元，求甲花篮的应降价是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆江北 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-01", "keyPointIds": "16438|16463", "keyPointNames": "和差倍分问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536336672411131904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "536336672411131904", "title": "2024年重庆市江北区九年级数学学业第一次质量试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536336944998948864", "questionArticle": "<p>8．一方有难八方支援，某市政府筹集了抗旱必需物资120吨打算运往灾区，现有甲、乙、丙三种车型供选择，每辆车的运载能力和运费如下表所示：（假设每辆车均满载）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p style=\"text-align:center;\">车型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">丙</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p style=\"text-align:center;\">汽车运载量（吨/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">10</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p style=\"text-align:center;\">汽车运费（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">300</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">500</p></td></tr></table><p>(1)若全部物资都用甲、乙两种车型来运送，需运费6400元，问分别需甲、乙两种车型各几辆？</p><p>(2)该市政府决定甲、乙、丙三种车型至少两种车型参与运送，已知它们的总辆数为18辆，请通过列方程组的方法分别求出三种车型的数量．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽池州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-01", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536336936744558592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536336936744558592", "title": "安徽省池州市贵池区2024−2025学年上学期七年级期末检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536336943757434880", "questionArticle": "<p>9．解方程（组）：</p><p>(1) $ \\dfrac { 2x-1 } { 4 }=1-\\dfrac { 7-5x } { 12 } $ </p><p>(2) $ \\begin{cases} x=y-2 \\\\ 3x+2y=-1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽池州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-01", "keyPointIds": "16402|16423|16424", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536336936744558592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536336936744558592", "title": "安徽省池州市贵池区2024−2025学年上学期七年级期末检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "536336943119900672", "questionArticle": "<p>10．已知关于 $ x、y $ 的方程组 $ \\begin{cases} 3x+y=3 \\\\ kx+\\left ( { k-1 } \\right ) y=6 \\end{cases}  $ 的解满足 $ x+y=1 $ ，求 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽池州 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-01", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "536336936744558592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "536336936744558592", "title": "安徽省池州市贵池区2024−2025学年上学期七年级期末检测数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}]}}, "requestData": {"pageNum": 209, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 209, "timestamp": "2025-07-01T02:25:28.749Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}