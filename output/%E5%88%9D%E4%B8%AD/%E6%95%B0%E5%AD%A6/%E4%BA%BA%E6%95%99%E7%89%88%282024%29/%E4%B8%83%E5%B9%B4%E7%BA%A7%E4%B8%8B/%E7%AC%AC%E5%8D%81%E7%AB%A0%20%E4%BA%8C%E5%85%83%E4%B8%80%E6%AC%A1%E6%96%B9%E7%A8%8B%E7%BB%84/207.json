{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 206, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "542361035866415104", "questionArticle": "<p>1．若二元一次方程组 $ \\begin{cases} \\dfrac { 2x-3y } { 6 }=4 \\\\ \\dfrac { 15x+15y-5 } { 3 }=0 \\end{cases}  $ 的解为 $ \\begin{cases} x=a \\\\ y=b \\end{cases}  $ ，则 $ a-b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\dfrac { 5 } { 3 } $　　　　B． $ \\dfrac { 9 } { 5 } $　　　　C． $ \\dfrac { 29 } { 3 } $　　　　D． $ -\\dfrac { 139 } { 3 } $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西实验中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542361031835688960", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542361031835688960", "title": "陕西省咸阳实验中学2024−2025学年八年级上学期期末质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542359329183145984", "questionArticle": "<p>2．解方程（组）：</p><p>(1) $ x-\\dfrac { x-1 } { 3 }=\\dfrac { 2x+1 } { 2 } $ ；</p><p>(2) $ \\begin{cases} 3x+2y=9 \\\\ 2x+y=7 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮南 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542359322065412096", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542359322065412096", "title": "安徽省淮南市2024−2025学年七年级上学期1月期末检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542359328260399104", "questionArticle": "<p>3．若 $ \\left ( { a-3 } \\right ) x{^{b}}+y{^{a{^{2}}-8}}=0 $ 是关于<i>x</i><i>，</i><i>y</i>的二元一次方程，则 $ a+b $ 的值<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|340000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025重庆万州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-02-06", "keyPointIds": "16287|16419", "keyPointNames": "平方根|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560236378417569792", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "560236378417569792", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}, {"id": "542359322065412096", "title": "安徽省淮南市2024−2025学年七年级上学期1月期末检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542359429838053376", "questionArticle": "<p>4．下列说法中正确的个数有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①若 $ a,\\,b $ 满足 $ a{^{2}}+b{^{2}}=6a+2b-10 $ ，则 $ a=3, b=1 $ ；</p><p>②关于 $ a,\\,b $ 的方程 $ 2a+4b=2025 $ 存在整数解；</p><p>③若两个实数 $ a,\\,b $ 满足 $ 2\\left ( { a{^{4}}+b{^{4}} } \\right ) ={\\left( { a{^{2}}+b{^{2}} } \\right) ^ {2}} $ ，则 $ a=b $ ；</p><p>④若 $ {\\left( { a-c } \\right) ^ {2}}-\\left ( { 2a-b } \\right ) \\left ( { b-2c } \\right ) =0 $ ，则 $ b=a+c $ ．</p><p>A．1个B．2个C．3个D．4个</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16332|16420", "keyPointNames": "完全平方公式|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542359423718563840", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542359423718563840", "title": "福建省福州第十九中学2024—2025学年八年级上学期数学期末试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542359326414905344", "questionArticle": "<p>5．已知关于 $ x $ ， $ y $ 的二元一次方程组的解 $ \\begin{cases} 3x+2y=k+1 \\\\ 2x+3y=k \\end{cases}  $ 满足 $ x+y=3 $ ，则 $ k $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1　　　　B．5　　　　C．7　　　　D．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮南 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542359322065412096", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542359322065412096", "title": "安徽省淮南市2024−2025学年七年级上学期1月期末检测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542359573333581824", "questionArticle": "<p>6．解二元一次方程组： $ \\begin{cases} x+y=4 \\\\ 2x-y=-1 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025福建宁德 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542359566173904896", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542359566173904896", "title": "福建省宁德市2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542359571089629184", "questionArticle": "<p>7．小明带50元去商店买作业本和笔，作业本的单价为5元，笔的单价为2元．购买作业本<i>a</i>本，笔<i>b</i>支，他的钱刚好够用 $ {\\rm ．\\mathit{a}} $ 的值可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．7　　　　B．8　　　　C．8.8　　　　D．9</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建宁德 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542359566173904896", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542359566173904896", "title": "福建省宁德市2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542359569038614528", "questionArticle": "<p>8．在下列二元一次方程中，有一组解为 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+y=3 $　　　　B． $ x-y=-3 $　　　　C． $ x+y=1 $　　　　D． $ x-y=1 $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025福建宁德 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-06", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542359566173904896", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542359566173904896", "title": "福建省宁德市2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537734793099780096", "questionArticle": "<p>9．随着我国网球名将郑钦文在巴黎奥运会中获得网球女子单打冠军，全国各地掀起了一股网球热，与网球有关的用品销量剧增，某厂家计划生产甲、乙两种品牌的网球拍共5000个，两种品牌的网球拍的成本和售价如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>乙</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>成本（元/个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>180</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>320</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>售价（元）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>230</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>400</p></td></tr></table><p>(1)该厂家计划用118万元资金全部生产甲、乙两种品牌的网球拍，则生产这两种品牌的网球拍各多少个？</p><p>(2)经过市场调研，该厂家决定在原计划的基础上增加生产甲网球拍 $ a $ 百个，乙网球拍 $ b $ 百个（ $ a,b $ 均为正整数），且两种品牌的网球拍售完后所获得的总利润为40万元，请问该厂家有几种生产方案？该厂家最少需投资多少万元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川成都 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537734783188639744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537734783188639744", "title": "四川省成都市八区联考2024−2025学年八年级上学期数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537734790801301504", "questionArticle": "<p>10．（1）计算： $ \\sqrt { 18 }-{\\left( { 2025-{ \\rm{ π } } } \\right) ^ {0}}-\\left  | { -\\sqrt { 2 } } \\right  | +{\\left( { \\dfrac { 1 } { 2 } } \\right) ^ {-2}} $ ．</p><p>（2）解方程组： $ \\begin{cases} 5x-y=-6 & ① \\\\ 5x+3y=-2 & ② \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-05", "keyPointIds": "16323|16372|16424", "keyPointNames": "零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537734783188639744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537734783188639744", "title": "四川省成都市八区联考2024−2025学年八年级上学期数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 207, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 207, "timestamp": "2025-07-01T02:25:14.747Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}