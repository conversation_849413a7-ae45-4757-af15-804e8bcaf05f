{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 199, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544317733946564608", "questionArticle": "<p>1．在“幻方拓展课程”探索中，小明在如图的3×3方格内填入了一些表示数的代数式，若图中各行、各列及对角线上的三个数之和都相等，则<i>x</i>－<i>y</i>＝（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/11/2/1/0/0/0/544317717471338496/images/img_1.png\" style=\"vertical-align:middle;\" width=\"63\" alt=\"试题资源网 https://stzy.com\"></p><p>A．2&nbsp;&nbsp;\t\tB．4&nbsp;&nbsp;\t\t\t</p><p>C．6&nbsp;&nbsp;\t\tD．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317956487946240", "questionArticle": "<p>2．甲、乙二人分别从相距 $ 40\\mathrm{k}\\mathrm{m} $ 的A、B两地出发，相向而行，如果甲比乙早出发1小时，那么乙出发后2小时，他们相遇；如果他们同时出发，那么2.5小时后，两人相距 $ 5\\mathrm{k}\\mathrm{m} $ ，则甲由A地到B地需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>小时.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317733640380416", "questionArticle": "<p>3．现有面额为100元和20元的人民币共33张，总面额为1 620元，则其中面额为100元的人民币有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．12张&nbsp;&nbsp;B．14张&nbsp;&nbsp;C．20张&nbsp;&nbsp;D．21张</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317956227899392", "questionArticle": "<p>4．已知甲地到乙地的公路，只有上坡路和下坡路，没有平路，一辆汽车上坡时速度为 $ 20\\mathrm{k}\\mathrm{m}/\\mathrm{h} $ ，下坡时速度为 $ 35\\mathrm{k}\\mathrm{m}/\\mathrm{h} $ ，车从甲地开往乙地需9小时，若从乙地返回甲地上下坡的速度不变，所需时间为7.5小时，那么甲地到乙地的公路长为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 300\\mathrm{k}\\mathrm{m} $ B． $ 210\\mathrm{k}\\mathrm{m} $ C． $ 200\\mathrm{k}\\mathrm{m} $ D． $ 150\\mathrm{k}\\mathrm{m} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318164496064512", "questionArticle": "<p>5．某中学组织初一年级学生春游，原计划租用45座客车若干辆，但有15人没有座位．若租用同样数量的60座客车，则多出一辆，且其余客车恰好坐满．已知45座客车每日租金为每辆220元，60座客车每日租金为每辆300元．</p><p>(1)初一年级人数是多少？原计划租用45座客车多少辆？</p><p>(2)可以单独租一种车，也可以同时租两种车，要使每个学生都有座位且每辆车刚好坐满，怎样租用更合算？(通过计算加以说明)</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420|16441", "keyPointNames": "二元一次方程的解|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317955829440512", "questionArticle": "<p>6．甲、乙二人都以不变的速度在环形路上跑步，如果同时同地出发，反向而行，每隔 $ 2 \\min  $ 相遇一次；如果同时同地出发，同向而行，每隔 $ 6 \\min  $ 相遇一次．已知甲比乙跑得快，则甲每分钟跑（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\dfrac{1}{2} $  圈B． $ \\dfrac{1}{3} $  圈C． $ \\dfrac{1}{4} $  圈D． $ \\dfrac{1}{6} $  圈</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317954776670208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317954776670208", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时3 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318076344377344", "questionArticle": "<p>7．明明和丽丽去书店买书，若已知明明买了A、B两种书各一本，共花费100.5元，丽丽买了A、C两种书各一本，共花费88.5元，则一本B书比一本C书贵<u>&nbsp;&nbsp;</u>元；若又知一本B书和一本C书的总价钱恰好等于一本A书的价钱，则买A、B、C三种书各一本的总价钱为<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>元.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16443|16444", "keyPointNames": "解三元一次方程组|三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318164231823360", "questionArticle": "<p>8．解方程组：</p><p>（1）  $ \\begin{cases}2x+5y=-14,\\mathrm{①}\\\\ 4x-y=16；\\mathrm{②}\\end{cases} $ </p><p>（2）  $ \\begin{cases}x-y+z=0,\\mathrm{①}\\\\ 4x+y+z=5,\\mathrm{②}\\\\ 9x+3y+z=16.\\mathrm{③}\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424|16443", "keyPointNames": "加减消元法解二元一次方程组|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318076117884928", "questionArticle": "<p>9．若 $ x $ ， $ y $ ， $ z $ 同时满足 $ x+y=13 $ ， $ y+z=12 $ ， $ x+z=5 $ ，则 $ 4x+4y+3z= $ <u>&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318073689382912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318073689382912", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.4 三元一次方程组的解法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544318164030496768", "questionArticle": "<p>10．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases}x+2y=-a+1,\\\\ x-3y=4a+6\\end{cases} $ （ $ a $ 是常数），若不论 $ a $ 取何值，代数式 $ kx-y $ （ $ k $ 是常数）的值始终不变，则 $ k= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 200, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 200, "timestamp": "2025-07-01T02:24:29.271Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}