{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 226, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "527581609756762112", "questionArticle": "<p>1．已知 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ 是方程 $ ax+y=7 $ 的一个解，那么常数 $ a $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5B． $ -5 $ C．3D． $ -3 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024广东深圳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-06", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527581605944139776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "527581605944139776", "title": "广东省深圳市光明区2024−2025学年八年级上学期期末数学模拟试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527582822552346624", "questionArticle": "<p>2．现在有蓝花布与红花布共40米，共卖了700元，已知4米蓝花布定价90元，3米红花布定价50元，问两种布各有多少米？设有蓝花布 $ x $ 米，红花布 $ y $ 米，依据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=40 \\\\ \\dfrac { 90 } { 4 }x+\\dfrac { 50 } { 3 }y=700 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=40 \\\\ \\dfrac { 90 } { 3 }x+\\dfrac { 50 } { 4 }y=700 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=40 \\\\ \\dfrac { 90 } { 3 }y+\\dfrac { 50 } { 4 }x=700 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=40 \\\\ \\dfrac { 50 } { 3 }x+\\dfrac { 90 } { 4 }y=700 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024吉林长春 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-06", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527582813249380352", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "527582813249380352", "title": "吉林省长春市2024−2025学年九年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527896255806087168", "questionArticle": "<p>3．解方程组： $ \\begin{cases} x-2y=1 \\\\ 3x+4y=23 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024广西西大附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-05", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527896247203569664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527896247203569664", "title": "广西壮族自治区南宁市西乡塘区广西大学附属中学2024—2025学年九年级第测试数学试题四次阶段性（12月）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527141560514813952", "questionArticle": "<p>4．我们规定，关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $ ，若满足 $ a+b=c $ ，则称这个方程为“幸福”方程．例如：方程 $ 2x+3y=5 $ ，其中 $ a=2 $ ， $ b=3 $ ， $ c=5 $ ，满足 $ a+b=c $ ，则方程 $ 2x+3y=5 $ 是“幸福”方程，把两个“幸福”方程合在一起叫“幸福”方程组，根据上述规定，回答下列问题．</p><p>(1)判断方程 $ 3x+5y=8 $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>“幸福”方程（填“是”或“不是”）；</p><p>(2)若关于<i>x</i>，<i>y</i>的二元一次方程 $ kx+（k-1）y=9 $ 是“幸福”方程，求<i>k</i>的值；</p><p>(3)若 $ \\begin{cases} x=p \\\\ y=q \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的“幸福”方程组 $ \\begin{cases} mx+(m+1)y=n-1 \\\\ mx+2my=n \\end{cases}  $ 的解，求 $ 4p+7q $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南平顶山 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-04", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141551950045184", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527141551950045184", "title": "河南省平顶山市等2地2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527141559222968320", "questionArticle": "<p>5．解下列方程组：</p><p>(1) $ \\begin{cases} 2y-2x=2 \\\\ 2x+2y=8 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x } { 2 }+\\dfrac { y } { 3 }=\\dfrac { 13 } { 2 } \\\\ 4x-3y=18 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南平顶山 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-04", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141551950045184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527141551950045184", "title": "河南省平顶山市等2地2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527141558002425856", "questionArticle": "<p>6．已知<i>x</i>，<i>y</i>为二元一次方程组 $ \\begin{cases} x+y=1 \\\\ 3x-y=3 \\end{cases}  $  的解， 则  $ x-y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南平顶山 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-04", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141551950045184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527141551950045184", "title": "河南省平顶山市等2地2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527141556798660608", "questionArticle": "<p>7．已知 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是二元一次方程组 $ \\begin{cases} mx+ny=8 \\\\ nx-my=1 \\end{cases}  $ 的解，则 $ 2m-n $ 的平方根为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B．4C． $ \\pm \\sqrt { 2 } $ D． $ \\pm 2 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南平顶山 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-04", "keyPointIds": "16287|16420|16424", "keyPointNames": "平方根|二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141551950045184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527141551950045184", "title": "河南省平顶山市等2地2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "527141765716942848", "questionArticle": "<p>8．某校准备组织七年级340名学生参加北京夏令营，已知用3辆小客车和1辆大客车每次可运送学生105人；用1辆小客车和2辆大客车每次可运送学生110人；</p><p>(1)每辆小客车和每辆大客车各能坐多少名学生？</p><p>(2)若学校计划租用小客车<i>x</i>辆，大客车<i>y</i>辆，一次送完，且恰好每辆车都坐满；</p><p>①请你设计出所有的租车方案；</p><p>②若小客车每辆需租金4000元，大客车每辆需租金8000元，请选出最省钱的租车方案，并求出最少租金．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024黑龙江绥化 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-04", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141755923243008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527141755923243008", "title": "黑龙江省绥化市2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "527141764731281408", "questionArticle": "<p>9．（1）计算： $ 2\\left ( { \\sqrt { 3 }-1 } \\right ) -\\left  | { \\sqrt { 3 }-2 } \\right  | -\\sqrt[3] { -64 } $ </p><p>（2）解方程组： $ \\begin{cases} 2x+3y=80 \\\\ 3x+2y=95 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024黑龙江绥化 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-04", "keyPointIds": "16288|16299|16424", "keyPointNames": "算术平方根|实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527141755923243008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "527141755923243008", "title": "黑龙江省绥化市2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "526894813838352384", "questionArticle": "<p>10．美丽服装店购进<i>A</i>，<i>B</i>两种新式服装共25件，合计花费1900元，已知这两种服装的进价，标价如表所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>类型价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p><i>A</i>型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p><i>B</i>型</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>进价（元/件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>100</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>标价（元/件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>160</p></td></tr></table><p>(1)请利用二元一次方程组求这两种服装各购进的件数；</p><p>(2)如果<i>A</i>种服装按标价出售，<i>B</i>种服装按标价的8折出售，那么这批服装全部售完后，美丽服装店一共可获利多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安铁一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-03", "keyPointIds": "16437|16440", "keyPointNames": "销售利润问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "526894804258562048", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "526894804258562048", "title": "陕西省西安市碑林区铁一中2024—2025学年上学期八年级第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 227, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 227, "timestamp": "2025-07-01T02:27:34.090Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}