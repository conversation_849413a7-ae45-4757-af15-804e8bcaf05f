{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 244, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "517448981024120832", "questionArticle": "<p>1．重庆被称为“三大火炉”城市之一，夏天尤其炎热，空调成为了重庆人民必不可少的电器．某电器超市销售每台进价分别为2800元、2000元的<i>A</i>、<i>B</i>两种型号的空调，该超市近两周的销售情况如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售收入/元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i> 种型号/台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i> 种型号/台</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第 1 周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>25000</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第 2周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>35000</p></td></tr></table><p>（进价、售价均保持不变，利润 $ = $ 销售收入 $ \\mathbf{ - } $ 进货成本）</p><p>(1)求 <i>A</i><i>、</i><i>B</i>两种型号的空调的销售单价；</p><p>(2)若超市准备用不超过13万元的金额再采购这两种型号的空调共50台，求<i>A</i>种型号的空调最多能采购多少台？</p><p>(3)在（2）的条件下，超市销售完这50台空调能否实现利润不低于 $ 57000 $ 元的目标？若能，请给出相应的采购方案：若不能，请说明理由．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-06", "keyPointIds": "16424|16438|16485|16486", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|解一元一次不等式|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448971977007104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "517448971977007104", "title": "重庆杨家坪中学2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}, {"id": "463903180155297792", "title": "重庆市巴南区2023-2024学年七年级下学期数学期末试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "517448980105568256", "questionArticle": "<p>2．（1）解方程组： $ \\begin{cases} 2x+3y=10 \\\\ 4x+y=5 \\end{cases}  $ ；</p><p>（2）解不等式组： $ \\begin{cases} 5-\\dfrac { 1 } { 2 }x\\geqslant  \\dfrac { 3x-6 } { 2 } \\\\ 3+x &gt; 4 \\end{cases}  $ ，并把解集在数轴上表示出来.</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/11/28/2/1/0/0/0/517448940364537859/images/img_24.png\" style=\"vertical-align:middle;\" width=\"344\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2024-12-06", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448971977007104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "517448971977007104", "title": "重庆杨家坪中学2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}, {"id": "463903434325925888", "title": "重庆市江津区2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "517448739130220544", "questionArticle": "<p>3．已知正整数<i>a</i>，<i>b</i>，<i>c</i>，<i>d</i>满足 $ a  &lt;  b  &lt;  c  &lt;  d $ ，且 $ a+b+c+d=d{^{2}}-c{^{2}}+b{^{2}}-a{^{2}} $ ，下列几个说法：① $ a=1 $ ， $ b=2 $ ， $ c=3 $ ， $ d=4 $ 是该四元方程的一组解；②连续的四个正整数一定是该四元方程的解；③若 $ d\\leqslant  6 $ ，则该四元方程有6组解．其中错误说法的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B．1C．2D．3</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-06", "keyPointIds": "16346|16353|16420", "keyPointNames": "综合运用公式法分解因式|因式分解的应用|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448732801015808", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "517448732801015808", "title": "重庆市第一一〇中学校集团五校联考2024−2025学年九年级上学期数学期中期学情调查试题", "paperCategory": 1}, {"id": "504053689016950784", "title": "重庆市第十八中学2024−2025学年九年级上学期9月学习能力摸底考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "517448609199071232", "questionArticle": "<p>4．若关于 $ x $ , $ y $ 的方程 $ \\begin{cases} 5y-x=2-m \\\\ 3x+y=-2+3m \\end{cases}  $ 的解满足 $ x-y=3 $ ，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆十一中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-06", "keyPointIds": "16402|16420|16424", "keyPointNames": "解一元一次方程|二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448602077143040", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "517448602077143040", "title": "重庆十一中教育集团2024−2025学年八年级上学期期中数学试题", "paperCategory": 1}, {"id": "362364053220532224", "title": "重庆育才中学数育集团2023-2024学年八年级上学期入学定时练习数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "519614849027973120", "questionArticle": "<p>5．若直线<i>y</i>=-<i>x</i>+<i>m</i>与直线<i>y</i>=2<i>x</i>+4的交点在第二象限，则<i>m</i>的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2  &lt;  m  &lt;  4 $ B． $ -2  &lt;  m  &lt;  3 $ C． $ -1  &lt;  m  &lt;  3 $ D． $ -1  &lt;  m  &lt;  4 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽合肥 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-06", "keyPointIds": "16420|16489", "keyPointNames": "二元一次方程的解|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519614843453743104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519614843453743104", "title": "安徽省合肥寿春中学2024−2025学年八年级上学期 数学期中试卷", "paperCategory": 1}, {"id": "174828137880002560", "title": "安徽省合肥市高新区2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "516744803326926848", "questionArticle": "<p>6．解二元一次方程方程组：</p><p>(1) $ \\begin{cases} x+y=9① \\\\ 3x+2y=21② \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x+5y=-9① \\\\ 2x-3y=13② \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024广东深圳 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-04", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516744797656227840", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516744797656227840", "title": "广东省深圳市福田区外国语学校（集团）2024−2025学年八年级上学期11月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "519269697411391488", "questionArticle": "<p>7．近期，国风矿物质颜料在网络上大火，引得各绘画爱好者争先购买．其中“岩灰”和“石绿”风靡一时，1瓶“岩灰”和1瓶“石绿”总价100元，“石绿”比“岩灰”单价高40元．</p><p>(1)分别求出“岩灰”和“石绿”的销售单价；</p><p>(2)某同学欲购买两种颜料共10瓶，预算资金不超过400元，则该同学最多可以购买多少瓶“石绿”？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州市第二中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-04", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519269689039560704", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "519269689039560704", "title": "浙江省温州市第二中学2024−2025学年八年级上学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "519269615249170432", "questionArticle": "<p>8．对<i>m</i>、<i>n</i>定义一种新运算“ $ \\nabla  $ ”，规定： $ m\\nabla n=am-bn+5 $ （其中<i>a</i>、<i>b</i>均为非零常数），等式右边的运算是通常的四则运算，例如： $ 5\\nabla 6=5a-6b+5 $ ．</p><p>(1)已知 $ 2\\nabla 3=1 $ ， $ 3\\nabla \\left ( { -1 } \\right ) =10 $ ．</p><p>①求<i>a</i>、<i>b</i>的值．</p><p>②若关于<i>x</i>的不等式组 $ \\begin{cases} x\\nabla \\left ( { 2x-3 } \\right )   &lt;  9 \\\\ 3x\\nabla \\left ( { -6 } \\right ) \\leqslant  t \\end{cases}  $ 有且只有两个整数解，求字母<i>t</i>的取值范围．</p><p>(2)若运算“ $ \\nabla  $ ”满足加法交换律，即对于我们所学过的任意数<i>m</i>、<i>n</i>，结论“ $ m\\nabla n=n\\nabla m $ ”都成立，试探究<i>a</i>、<i>b</i>应满足的关系．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江宁波 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-04", "keyPointIds": "16353|16424|16489", "keyPointNames": "因式分解的应用|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519269606894116864", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "519269606894116864", "title": "浙江省宁波市八校联考2024−2025学年上学期八年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516745027617333248", "questionArticle": "<p>9．阅读下面的诗句：“栖树一群鸦，鸦树不知数，三只栖一树，五只没去处，五只栖一树，闲了一棵树，请你仔细数，鸦树各几何？”大意是：“一群乌鸦在树上栖息，若每棵树上有3只，则5只没地方去，若每棵树上有5只，则多了一棵树．”设乌鸦 $ x $ 只．树 $ y $ 棵，依题意可列方程组：（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3y+5=x \\\\ 5(y-1)=x \\end{cases}  $ B． $ \\begin{cases} 3x+5=y \\\\ 5(x-1)=y \\end{cases}  $ C． $ \\begin{cases} 3y+5=x \\\\ 5y=x-5 \\end{cases}  $ D． $ \\begin{cases} 3y=x+5 \\\\ 5y=x-5 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|450000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024广西广西壮族自治区南宁市第三中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-04", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516745021946634240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "516745021946634240", "title": "广西南宁市第三中学2024−2025学年八年级数学上学期期中考试题", "paperCategory": 1}, {"id": "492113550854365184", "title": "山东省青岛市崂山育才中学学校2024−2025学年九年级上学期期初考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "519269615073009664", "questionArticle": "<p>10．某电器超市销售<i>A</i> <i>B</i>两种型号的电风扇，<i>A</i>型号每台进价为200元，<i>B</i>型号每台进价分别为150元，下表是近两天的销售情况:</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i>种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i>种型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一天</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1620元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二天</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>10台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>2760元</p></td></tr></table><p>(进价、售价均保持不变，利润=销售收入-进货成本)</p><p>(1)求<i>A</i>、<i>B</i>两种型号的电风扇的销售单价;</p><p>(2)若超市准备用不多于5400元的金额再采购这两种型号的电风扇共30台，求<i>A</i>种型号的电风扇最多能采购多少台?</p><p>(3)在(2)的条件下,超市销售完这30台电风扇能否实现利润不少于1060元的目标?若能，请给出相应的采购方案;若不能，请说明理由.</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000|450000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江宁波 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 3, "createTime": "2024-12-04", "keyPointIds": "16424|16434|16438|16486", "keyPointNames": "加减消元法解二元一次方程组|方案问题|和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519269606894116864", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519269606894116864", "title": "浙江省宁波市八校联考2024−2025学年上学期八年级期中数学试卷", "paperCategory": 1}, {"id": "464293954126651392", "title": "江西省宜春市2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "493852063371141120", "title": "广西南宁市青秀区三美学校2024−2025学年八年级上学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 245, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 245, "timestamp": "2025-07-01T02:29:47.271Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}