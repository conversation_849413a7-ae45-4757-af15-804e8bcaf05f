{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 225, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "531853742657806336", "questionArticle": "<p>1．在解方程组 $ \\begin{cases} 2ax+y=5 \\\\ 2x-by=13 \\end{cases}  $ 时，由于粗心，甲看错了方程组中的 $ a $ ，得解为 $ \\begin{cases} x=\\dfrac { 7 } { 2 } \\\\ y=-2 \\end{cases}  $ ；乙看错了方程组中的<i>b</i>，得解为 $ \\begin{cases} x=3 \\\\ y=-7 \\end{cases}  $ ．</p><p>(1)甲把<i>a</i>错看成了什么？乙把<i>b</i>错看成了什么？</p><p>(2)求出原方程组的正确解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531853734009151488", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "531853734009151488", "title": "湖南省永州市冷水滩区李达中学2024−2025学年七年级上学期第三次检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "531853743123374080", "questionArticle": "<p>2．某校准备组织九年级340名学生参加北京夏令营，已知用3辆小客车和1辆大客车每次可运送学生105人；用1辆小客车和2辆大客车每次可运送学生110人；</p><p>(1)每辆小客车和每辆大客车各能坐多少名学生？</p><p>(2)若学校计划租用小客车<i>x</i>辆，大客车<i>y</i>辆，一次送完，且恰好每辆车都坐满，若小客车每辆需租金4000元，大客车每辆需租金8000元，请选出最省钱的租车方案，并求出最少租金．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16420|16441", "keyPointNames": "二元一次方程的解|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531853734009151488", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "531853734009151488", "title": "湖南省永州市冷水滩区李达中学2024−2025学年七年级上学期第三次检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "531853741349183488", "questionArticle": "<p>3．关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=4 \\\\ x+2y=m \\end{cases}  $ 的解满足 $ x+y=1 $ ，则<i>m</i>的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖南永州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531853734009151488", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "531853734009151488", "title": "湖南省永州市冷水滩区李达中学2024−2025学年七年级上学期第三次检测数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "531853742217404416", "questionArticle": "<p>4．解方程或方程组：</p><p>(1) $ \\dfrac { 2x+5 } { 6 }-\\dfrac { 3x-2 } { 8 }=1 $ ；</p><p>(2) $ \\begin{cases} \\dfrac { a+3b } { 2 }=\\dfrac { 3 } { 5 } \\\\ 5\\left ( { a-2b } \\right ) =-4 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531853734009151488", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "531853734009151488", "title": "湖南省永州市冷水滩区李达中学2024−2025学年七年级上学期第三次检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "531853739616935936", "questionArticle": "<p>5．《九章算术》是中国古代的一本重要数学著作，其中有一道方程的应用题：“今有五只雀、六只燕，分别聚集而且用衡器称之，聚在一起的雀重、燕轻，将一只雀、一只燕交换位置而放，则衡器两边的总重量相等，如果五只雀和六只燕的总重量为十六两，问每只雀、燕的重量各为多少两？”解：设每只雀重<i>x</i>两，每燕只重<i>y</i>两，则可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+y=4y+x \\\\ 5x+6y=16 \\end{cases}  $　　　　B． $ \\begin{cases} 4x+y=5y+x \\\\ 6x+5y=16 \\end{cases}  $　　　　C． $ \\begin{cases} 5x+y=4y+x \\\\ 6x+5y=16 \\end{cases}  $　　　　D． $ \\begin{cases} 4x+y=5y+x \\\\ 5x+6y=16 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531853734009151488", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "531853734009151488", "title": "湖南省永州市冷水滩区李达中学2024−2025学年七年级上学期第三次检测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "531853666195644416", "questionArticle": "<p>6．解方程组：</p><p>(1) $ \\begin{cases} 3x+4y=16 \\\\ 6x+9y=25 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2x-3y+5=0 \\\\ \\dfrac { 6y-4x+3 } { 7 }=2y+1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖南娄底 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531853656490024960", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "531853656490024960", "title": "湖南省娄底市第二中学2024-−2025学年七年级上学期期末数学模拟考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "531595101694369792", "questionArticle": "<p>7．阅读下列材料，并解答问题：</p><p>将分式 $ \\dfrac { x{^{2}}-x+3 } { x+1 } $ 拆分成一个整式与一个分式（分子为整数）的和的形式．</p><p>解：由分母<i>x</i>＋1，可设 $ x{^{2}}-x+3=(x+1)(x+a)+b $ ,</p><p>则 $ x{^{2}}-x+3=(x+1)(x+a)+b=x{^{2}}+ax+x+a+b=x{^{2}}+(a+1)x+a+b $ ．</p><p>∵对于任意<i>x</i>上述等式成立，</p><p>∴ $ \\begin{cases} a+1=-1 \\\\ a+b=3 \\end{cases}  $ 解得 $ \\begin{cases} a=-2 \\\\ b=5 \\end{cases}  $ ，</p><p>∴ $ \\dfrac { x{^{2}}-x+3 } { x+1 }=\\dfrac { (x+1)(x-2)+5 } { x+1 }=x-2+\\dfrac { 5 } { x+1 } $ ．</p><p>这样，分式 $ \\dfrac { x{^{2}}-x+3 } { x+1 } $ 就拆分成一个整式<i>x</i>−2与一个分式 $ \\dfrac { 5 } { x+1 } $ 的和的形式．</p><p>(1)将分式 $ \\dfrac { x{^{2}}+6x-3 } { x-1 } $ 拆分成一个整式与一个分式（分子为整数）的和的形式为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> ；</p><p>(2)已知整数<i>x</i>使分式 $ \\dfrac { 2x{^{2}}+5x-20 } { x-3 } $ 的值为整数，求满足条件的整数<i>x</i>的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024黑龙江哈尔滨 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16361|16423|16471", "keyPointNames": "分式的基本性质|代入消元法解二元一次方程组|解分式方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531595094287228928", "questionFeatureName": "阅读材料题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "531595094287228928", "title": "黑龙江省哈尔滨市南岗区萧红中学2024−2025学年八年级上学期数学期末复习", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "531595270821289984", "questionArticle": "<p>8．“靖州杨梅”——湖南省靖州县特产，全国农产品地理标志．靖州杨梅已有上千年的栽培史，以色泽呈乌、酸甜适度、果大核小、品质优良、营养丰富而著称．《靖州乡土志》诗云：“木洞杨梅尤擅名，申园梨栗亦争鸣，百钱且得论摊买，恨不移根植上京．”目前，靖州杨梅主要分为台梅和乌梅两种．某水果商为了解靖州杨梅的市场销售情况，购进台梅和乌梅两种进行试销．在试销中，水果商将两种杨梅搭配销售，若购买台梅4千克，乌梅3千克，共需192元；若购买台梅3千克，乌梅4千克，共需172元．</p><p>(1)求台梅和乌梅每千克各多少元？</p><p>(2)一顾客用不超过2600元购买这两种杨梅共100千克，要求台梅尽量多，他最多能购买台梅多少千克？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖南怀化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-08", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531595261476380672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "531595261476380672", "title": "湖南省怀化市五县六校联考2024−2025学年八年级上学期12月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "531596500578639872", "questionArticle": "<p>9．对任意一个三位数 $ n $ ，如果满足各个数位上的数字互不相同，且都不为零，那么称这个数为“相异数”，将一个“相异数”任意两个数位上的数字对调后可以得到三个不同的新三位数，把这三个新三位数和与111的商记为 $ F(n) $ ．例如： $ n=123 $ ，对调百位与十位上的数字得 $ n{{}_{ 1 } }=213 $ ，对调百位与个位上的数字得 $ n{{}_{ 2 } }=321 $ ，对调十位与个位上的数字得 $ n{{}_{ 3 } }=132 $ ，这三个新三位数的和为 $ 213+321+132=666 $ ， $ 666\\div 111=6 $ ，所以 $ F(123)=6 $ ．则， $ F(512) $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；若 $ s,t $ 都是“相异数”，其中 $ s=100x+32 $ ， $ t=270+y $ （ $ 1\\leqslant  x\\leqslant  9 $ ， $ 1\\leqslant  y\\leqslant  9 $ ， $ x,y $ 都是正整数），规定： $ k=\\dfrac { F(s) } { F(t) } $ ，当 $ F(s)+F(t)=20 $ 时，则 $ k $ 的最大值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市江津第二中学 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-01-07", "keyPointIds": "16426|16433", "keyPointNames": "二元一次方程组的应用|数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531596493586735104", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "531596493586735104", "title": "重庆市江津二中联盟2024−2025学年上学期十校期末模拟联考七年级数学试题", "paperCategory": 1}, {"id": "418222152699125760", "title": "四川省成都市青羊区成都市石室联合中学2023-2024学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "527581612650831872", "questionArticle": "<p>10．解方程组；</p><p>(1) $ \\begin{cases} x-2y=1 \\\\ 3x-y=3 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 2x+3y=9 \\\\ \\dfrac { x } { 3 }-\\dfrac { y+1 } { 2 }=2 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024广东深圳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-01-06", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "527581605944139776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "527581605944139776", "title": "广东省深圳市光明区2024−2025学年八年级上学期期末数学模拟试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 226, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 226, "timestamp": "2025-07-01T02:27:26.736Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}