{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 204, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "542799257213902848", "questionArticle": "<p>1．随着全球对健康饮食和营养均衡的日益关注，许多学校开始重视学生的午餐质量．某配餐公司承接了为当地中学提供营养餐的任务，他们需要确保每餐都含有充足的蛋白质和碳水化合物，以满足学生日常学习和成长的需求．为了达到这一目标，该配餐公司决定使用两种主要的食材――甲食材和乙食材．这两种食材的营养成分如下表所示：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/23/2/1/0/0/0/558995136132194305/images/img_1.png\" style='vertical-align:middle;' width=\"159\" alt=\"试题资源网 https://stzy.com\"></p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.7pt;\"><p style=\"text-align:center;\">甲食材</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.1pt;\"><p style=\"text-align:center;\">乙食材</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">每克所含蛋白质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.7pt;\"><p style=\"text-align:center;\"> $ 0.3 $ 单位</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.1pt;\"><p style=\"text-align:center;\"> $ 0.75 $ 单位</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">每克所含碳水化合物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.7pt;\"><p style=\"text-align:center;\"> $ 0.6 $ 单位</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.1pt;\"><p style=\"text-align:center;\"> $ 0.5 $ 单位</p></td></tr></table><p>根据营养学专家的建议，每位中学生每餐需要摄入21单位的蛋白质和40单位的碳水化合物．那么，为了满足这些营养需求，每餐应该使用甲、乙两种食材各多少克?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542799247659278336", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "542799247659278336", "title": "重庆市实验外国语学校2024−2025学年上学期九年级数学期末考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542799256291155968", "questionArticle": "<p>2．解方程组：</p><p>(1) $ \\begin{cases} 3x+2y=14 \\\\ x=y+3 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2x-y=4 \\\\ x+3y=-5 \\end{cases}  $ .</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542799247659278336", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "542799247659278336", "title": "重庆市实验外国语学校2024−2025学年上学期九年级数学期末考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542799254709903360", "questionArticle": "<p>3．若 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 是方程<i>x</i>+<i>ay</i>＝3的一个解，则<i>a</i>的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542799247659278336", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "542799247659278336", "title": "重庆市实验外国语学校2024−2025学年上学期九年级数学期末考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542799253128650752", "questionArticle": "<p>4．如图为某商店的宣传单，小胜到此店同时购买了一件标价为<i>x</i>元的衣服和一条标价为<i>y</i>元的裤子，共节省500元，则根据题意所列方程正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/23/2/1/0/0/0/558994286844354561/images/img_1.png\" style='vertical-align:middle;' width=\"168\" alt=\"试题资源网 https://stzy.com\"></p><p>A．0.6<i>x</i>+0.4<i>y</i>+100=500　　　　B．0.6<i>x</i>+0.4<i>y</i>﹣100=500</p><p>C．0.4<i>x</i>+0.6<i>y</i>+100=500　　　　D．0.4<i>x</i>+0.6<i>y</i>﹣100=500</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542799247659278336", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "542799247659278336", "title": "重庆市实验外国语学校2024−2025学年上学期九年级数学期末考试试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "543199380989321216", "questionArticle": "<p>5．某商场从厂家购进甲、乙两种文具，甲种文具的每件进价比乙种文具的每件进价少20元．若购进甲种文具7件，乙种文具2件，则需要760元．</p><p>(1)求甲、乙两种文具的每件进价分别是多少元？</p><p>(2)该商场从厂家购进甲、乙两种文具共50件，所用资金恰好为4400元．求甲、乙两种文具的件数．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州第十八中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-02-08", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543199372487467008", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543199372487467008", "title": "福建省福州十八中2024−2025学年七年级上学期期末考试数学试卷", "paperCategory": 1}, {"id": "425206057528500224", "title": "广西壮族自治区南宁市青秀区三美学校2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543199377751318528", "questionArticle": "<p>6．二元一次方程 $ x-2y=1 $ 有无数多个解，下列四组值中不是该方程的解的是</p><p>A． $ \\begin{cases} x=0 \\\\ y=-\\dfrac { 1 } { 2 } \\end{cases}  $ B． $ \\begin{cases} x=1 \\\\ y=1 \\end{cases}  $ C． $ \\begin{cases} x=1 \\\\ y=0 \\end{cases}  $ D． $ \\begin{cases} x=-1 \\\\ y=-1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|350000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州第十八中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 9, "referenceNum": 4, "createTime": "2025-02-08", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543199372487467008", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "543199372487467008", "title": "福建省福州十八中2024−2025学年七年级上学期期末考试数学试卷", "paperCategory": 1}, {"id": "456233752030650368", "title": "北京市海淀区育英学校2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "209605252298874880", "title": "广东省潮州市湘桥区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "178084482183897088", "title": "北京市平谷区峪口中学2020-2021学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542798266334748672", "questionArticle": "<p>7．某共享单车运营公司准备采购一批共享单车投入市场，而共享单车安装公司由于抽调不出足够熟练工人，准备招聘一批新工人，已知2名熟练工人和3名新工人每天共安装44辆共享单车；4名熟练工人每天安装的共享单车数与5名新工人每天安装的共享单车数一样多．</p><p>(1)求每名熟练工人和新工人每天分别可以安装多少辆共享单车；</p><p>(2)共享单车安装公司计划抽调出不超过7名熟练工人，并且招聘若干新工人共同安装共享单车．如果25天后刚好交付运营公司3500辆合格品投入市场，求熟练工人和新工人各多少人．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆大渡口 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542798256880787456", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542798256880787456", "title": "重庆市大渡口区2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542798265688825856", "questionArticle": "<p>8．计算或解方程组：</p><p>(1) $ \\sqrt { 8 }+\\sqrt { 32 }-6\\sqrt { \\dfrac { 1 } { 2 } } $ ；</p><p>(2) $ \\begin{cases} 3x-2y=13 \\\\ 4x+y=10 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆大渡口 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-08", "keyPointIds": "16388|16424", "keyPointNames": "二次根式的加减运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542798256880787456", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542798256880787456", "title": "重庆市大渡口区2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542797988134952960", "questionArticle": "<p>9．簪花在我国已有两、三千年的历史．热爱传统文化的涵涵购买了若干支丁香花、海棠花、玉兰花用于手工制作三款簪花头饰各一套（每款均用到三种花）．已知每款簪花中海棠花的用量等于玉兰花用量．A款丁香花用量为3枝，B款丁香花用量比C款丁香花用量少2枝；A款中玉兰花的用量为2枝，B款玉兰花的用量是它的丁香花用量的3倍；制作完成后统计发现，三款簪花丁香花的总用量与玉兰花总用量比为 $ 11:13 $ ．已知每款簪花成本等于所用花朵成本之和．若每枝丁香花、海棠花、玉兰花的成本分别是 $ a $ 元、 $ b $ 元、 $ c $ 元，则C款簪花的成本是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元（用含 $ a $ ， $ b $ ， $ c $ 的代数式表示）．若A款簪花的成本为49元，B款簪花的成本为63元，则C款簪花的成本是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市巴蜀中学校 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-07", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542797978756489216", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "542797978756489216", "title": "重庆市巴蜀中学2024-−2025学年七年级上学期数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542766182727720960", "questionArticle": "<p>10．为了绿化校园,30名学生共种78棵树苗.其中,男生每人种3棵,女生每人种2棵,设该班男生有<i>x</i>人,女生有<i>y</i>人.根据题意,所列方程组正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>　　　　　　　　　　　 　　　　　　　　　　　 　　　　　　　　　　　 </p><p>A. $ \\begin{cases}x+y=78,\\\\ 3x+2y=30\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;\tB. $ \\begin{cases}x+y=78,\\\\ 2x+3y=30\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;\t</p><p> C. $ \\begin{cases}x+y=30,\\\\ 2x+3y=78\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;\tD. $ \\begin{cases}x+y=30,\\\\ 3x+2y=78\\end{cases} $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-02-07", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552902865309704192", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552902865309704192", "title": "第9讲 实践与探索（练习）", "paperCategory": 10}, {"id": "542766181343600640", "title": "第13讲　二元一次方程组的应用（练习）", "paperCategory": 10}, {"id": "444987974675111936", "title": "广东省广州市第八十九中学2023~2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 205, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 205, "timestamp": "2025-07-01T02:25:02.375Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}