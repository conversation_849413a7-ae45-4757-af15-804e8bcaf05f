{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 210, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "537019612581371904", "questionArticle": "<p>1．古书中有一个“隔沟计算”的问题，其大意如下：甲乙两人隔一条沟放牧，二人心里暗中合计．甲对乙说：“我得到你的九只羊，我的羊就比你多一倍．”乙对甲说：“我得到你的九只羊，咱俩的羊一样多．”设甲有羊 $ x $ 只，乙有羊 $ y $ 只，那么符合题意的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+9=2y \\\\ y+9=x \\end{cases}  $ B． $ \\begin{cases} 2x+9=y-9 \\\\ x-9=y+9 \\end{cases}  $ C． $ \\begin{cases} x-9=2y-9 \\\\ x+9=y-9 \\end{cases}  $ D． $ \\begin{cases} x+9=2\\left ( { y-9 } \\right )  \\\\ x-9=y+9 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙青竹湖湘一外国语学校 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537019605560107008", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537019605560107008", "title": "湖南省长沙市开福区长青竹湖湘一外国语学校2024−2025学年八年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537436690316566528", "questionArticle": "<p>2．某旅游纪念品商店销售 $ \\mathrm{ A } $ ， $ B $ 两种商品，已知销售一件 $ \\mathrm{ A } $ 种商品和两件 $ B $ 种商品可获利80元，销售三件 $ \\mathrm{ A } $ 种商品和一件 $ B $ 种商品可获利90元．</p><p>(1)求销售一件 $ \\mathrm{ A } $ 种商品和一件 $ B $ 种商品各获利多少元？</p><p>(2)该旅游纪念品商店计划一次性购进 $ \\mathrm{ A } $ ， $ B $ 两种商品共30件，其中 $ \\mathrm{ A } $ 种商品数量不少于10件，将其全部销售完可获总利润为 $ w $ 元．设购进 $ \\mathrm{ A } $ 种商品 $ a $ 件．</p><p>①求 $ w $ 与 $ a $ 的函数关系式；</p><p>②利用函数图象性质，当购进 $ \\mathrm{ A } $ 种商品多少件时，该商店可获利最大，最大利润是多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南平顶山 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-27", "keyPointIds": "16438|16535|16544", "keyPointNames": "和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537436682666156032", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537436682666156032", "title": "河南省平顶山市2024−2025学年上学期期末考试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537436689192493056", "questionArticle": "<p>3．（1）计算： $ 2\\sqrt { 12 }\\times \\sqrt { 3 }+(1-\\sqrt { 3 }){^{2}} $ ；</p><p>（2）解方程组： $ \\begin{cases} 3x+2y=14 \\\\ 5x-y=6 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南平顶山 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-27", "keyPointIds": "16389|16424", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537436682666156032", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537436682666156032", "title": "河南省平顶山市2024−2025学年上学期期末考试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537436687611240448", "questionArticle": "<p>4．用一根绳子环绕一棵大树，若环绕大树3周，则绳子还多4尺；若环绕大树4周，则绳子又少了3尺．这根绳子有多长？环绕大树一周需要多少尺？设这根绳子有 $ x $ 尺，环绕大树一周需要 $ y $ 尺，根据题意列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 4y+3=x \\\\ 3y+4=x \\end{cases}  $ B． $ \\begin{cases} 3y-4=x \\\\ 4y-3=x \\end{cases}  $ </p><p>C． $ \\begin{cases} 3y+4=x \\\\ 4y-3=x \\end{cases}  $ D． $ \\begin{cases} 4y+3=x \\\\ 3y-4=x \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南平顶山 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-27", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537436682666156032", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537436682666156032", "title": "河南省平顶山市2024−2025学年上学期期末考试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "537020044607266816", "questionArticle": "<p>5．某中学教室悬挂的一面窗帘布，上端是由14个圆环镶嵌在其中且间距相等，如图是窗帘布展开的平面示意图，已知一面窗帘布的宽度是156厘米，圆环间的间距<i>d</i>比圆环外圆的半径<i>R</i>多1厘米，两端边距的宽度都是10厘米．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/21/2/1/0/0/0/537020019252699138/images/img_9.png\" style=\"vertical-align:middle;\" width=\"389\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求<i>d</i>和<i>R</i>．</p><p>(2)已知圆环内圆的半径<i>r</i>比外圆的半径<i>R</i>少1厘米，制作一面窗帘需将内圆镂空裁去，求裁去的面积．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西九江 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-25", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537020034842927104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537020034842927104", "title": "江西省九江市2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537020042824687616", "questionArticle": "<p>6．解二元一次方程组： $ \\begin{cases} x-3y=3 \\\\ 3x-y=9 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西九江 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537020034842927104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537020034842927104", "title": "江西省九江市2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537021245881098240", "questionArticle": "<p>7．图中是一把学生椅，主要由靠背、座板及铁架组成，经测量，该款学生椅的座板尺寸为 $ 40{ \\rm{ c } }{ \\rm{ m } }\\times 35{ \\rm{ c } }{ \\rm{ m } } $ ，靠背由两块相同的靠背板组成，其尺寸均为 $ 40{ \\rm{ c } }{ \\rm{ m } }\\times 10{ \\rm{ c } }{ \\rm{ m } } $ ．</p><p>因学校需要，某工厂配合制作该款式学生椅，清点库存时发现，工厂仓库已有大量的学生椅铁架，故只需在市场上购进某型号板材加工制作该款式学生椅的靠背与座板，如下图，该型号板材长为 $ 240{ \\rm{ c } }{ \\rm{ m } } $ ，宽为 $ 50{ \\rm{ c } }{ \\rm{ m } } $ ．（裁切时不计损耗）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/01/23/2/20/0/0/0/537645577707757569/images/img_1.png\" style='vertical-align:middle;' width=\"320\" alt=\"试题资源网 https://stzy.com\"></p><p>【任务一】拟定裁切方案</p><p>（1）在不造成板材浪费的前提下，若将一张该板材全部用来裁切靠背板，则可裁切靠背板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>块．</p><p>（2）在不造成板材浪费的前提下，若将一张该板材同时裁切出靠背板和座板，请你设计出所有符合要求的裁切方案：</p><p>方案一：裁切靠背板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>块和座板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>块．</p><p>方案二：裁切靠背板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>块和座板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>块．</p><p>方案三：裁切靠背板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>块和座板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>块．</p><p>【任务二】确定搭配数量</p><p>（3）现需要制作700张学生椅，该工厂仓库现有10块靠背板，没有座板，请问还需要购买该型号板材多少张（恰好全部用完）？为方便加工，需在上述裁切方案中选定两种，并说出你选定的两种裁切方案分别需要多少块板材．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西陕西师范大学附属中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537021238130024448", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537021238130024448", "title": "陕西省西安市陕西师范大学附属中学2024-−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537437281298194432", "questionArticle": "<p>8．解方程组：</p><p>(1) $ \\begin{cases} 2x+4y=5 \\\\ x=1-y \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { y+1 } { 4 }=\\dfrac { x+2 } { 3 } \\\\ 2x-3y=1 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安铁一中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537437271684849664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537437271684849664", "title": "陕西省西安市铁一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "537437280526442496", "questionArticle": "<p>9．某旅馆的客房有三人间和两人间两种，三人间每人每天 $ 25 $ 元，两人间每人每天 $ 35 $ 元．一个 $ 79 $ 人的旅游团到该旅馆住宿，租住了若干客房，且每个客房正好住满，一天共花去住宿费 $ 2315 $ 元．设该旅游团租住三人间客房 $ x $ 间，两人间客房 $ y $ 间，请列出满足题意的方程组<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安铁一中学 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537437271684849664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537437271684849664", "title": "陕西省西安市铁一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "537020878447484928", "questionArticle": "<p>10．解方程组</p><p>(1) $ \\begin{cases} 2x-3y=17 \\\\ 5x-2y=26 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x } { 7 }+y=1 \\\\ 5x-7y=11 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西陕西师范大学附属中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-24", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537020870025322496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "537020870025322496", "title": "陕西省西安市陕西师范大学附属中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 211, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 211, "timestamp": "2025-07-01T02:25:42.209Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}