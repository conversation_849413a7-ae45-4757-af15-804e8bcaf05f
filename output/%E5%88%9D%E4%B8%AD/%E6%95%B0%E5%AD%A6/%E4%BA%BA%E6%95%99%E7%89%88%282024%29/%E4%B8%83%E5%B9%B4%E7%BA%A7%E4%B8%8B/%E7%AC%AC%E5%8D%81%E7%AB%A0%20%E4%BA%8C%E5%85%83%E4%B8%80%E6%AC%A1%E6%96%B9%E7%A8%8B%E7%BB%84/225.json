{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 224, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "532315587537575936", "questionArticle": "<p>1．（1）计算： $ \\dfrac { \\sqrt { 72 }-\\sqrt { 16 } } { \\sqrt { 8 } }+(\\sqrt { 3 }+1)(\\sqrt { 3 }-1) $ </p><p>（2）解方程组∶ $ \\begin{cases} x-y=1 \\\\ 4\\left ( { x-y } \\right ) -y=5 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-10", "keyPointIds": "16331|16389|16423", "keyPointNames": "平方差公式|二次根式的混合运算|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532315579505483776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532315579505483776", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（二）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "532315585176182784", "questionArticle": "<p>2．二元一次方程组 $ \\begin{cases} x+y=6 \\\\ x-3y=2 \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=4 \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=5 \\\\ y=1 \\end{cases}  $ C． $ \\begin{cases} x=-5 \\\\ y=-1 \\end{cases}  $ D． $ \\begin{cases} x=-4 \\\\ y=-2 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532315579505483776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532315579505483776", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（二）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "532701692443271168", "questionArticle": "<p>3．已知 $ x+y+7z=0 $ ， $ x-y-3z=0\\left ( { xyz\\ne 0 } \\right )  $ ，则 $ \\dfrac { 2x+y+z } { 2x-y+z }= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-10", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}, {"id": "394437362787328000", "title": "北京市清华大学附属中学2022-2023学年七年级上学期数学期末测试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "532701691382112256", "questionArticle": "<p>4．已知方程组 $ \\begin{cases} x+y=3 \\\\ y+z=-6 \\\\ z+x=9 \\end{cases}  $ ，则 $ x+y+z $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3 $ B． $ 4 $ </p><p>C． $ 5 $ D． $ 6 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 3, "createTime": "2025-01-10", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}, {"id": "461314004725571584", "title": "河北省邯郸市邯山区2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "198710777250881536", "title": "2022年七年级下册华师版数学第7章7.3三元一次方程组及其解法课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "531850385113260032", "questionArticle": "<p>5．《九章算术》第七卷“盈不足”中记载：“今有共买物，人出八，盈三；人出七，不足四、问人数、物价各几何？”译为：“今有几个人合伙购买一件物品，每人出8钱，会多3钱；每人出7钱，又差4钱．问人数和物品价格分别是多少？”设人数为<i>x</i>，则列出方程正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 8x-3=7x+4 $ B． $ 8x+3=7x-4 $ </p><p>C． $ 8(x-3)=7(x+4) $ D． $ 8(x+3)=7(x-4) $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙市长郡双语实验中学 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-01-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549406478546804736", "questionFeatureName": "数学文化题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "549406478546804736", "title": "湖南省长沙市长郡双语实验中学2024−2025学年九年级下学期开学数学试题", "paperCategory": 1}, {"id": "531850379861991424", "title": "山东省济南市市中区济南育英中学2024−2025学年七年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "531851001965355008", "questionArticle": "<p>6．麦麦蛋糕店准备促销“葡式蛋挞”和“香草泡芙”，已知“葡式蛋挞”的成本为10元/份，售价为20元/份，“香草泡芙”的成本为12元/份，售价为24元/份，第一天销售这两种蛋糕共136份，获利1438元．</p><p>(1)求第一天这两种蛋糕的销量分别是多少份；</p><p>(2)经过第一天的销售后，这两种蛋糕的库存发生了变化，为了更好的销售这两种蛋糕，店主决定把“葡式蛋挞”的售价在原来的基础上减少 $ 0.4a $ ，“香草泡芙”的售价在原来的基础上增加 $ 0.5a $ ，“葡式蛋挞”的销量在原来的基础上减少了12份，“香草泡芙”的销量在原来的基础上增加了31份，但两种蛋糕的成本不变，结果获利比第一天多254元．求 $ a $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆十一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-09", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531850990758174720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "531850990758174720", "title": "重庆市第十一中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "527503474591834112", "title": "重庆市第十一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "531851001017442304", "questionArticle": "<p>7．解下列方程组：</p><p>(1) $ \\begin{cases} 2x+y=4 \\\\ 7x+3y=15 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x+1 } { 2 }=\\dfrac { y+1 } { 3 } \\\\ 2\\left ( { x-y } \\right ) =8-3y \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆十一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-09", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531850990758174720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "531850990758174720", "title": "重庆市第十一中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "527503474591834112", "title": "重庆市第十一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "531850890463977472", "questionArticle": "<p>8．已知 $ \\dfrac { 3x-4 } { \\left ( { x-1 } \\right ) \\left ( { x-2 } \\right )  }=\\dfrac { A } { x-1 }+\\dfrac { B } { x-2 } $ ，则 $ A+B $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．3D．4</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-09", "keyPointIds": "16369|16424", "keyPointNames": "分式的加减|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531850883400769536", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "531850883400769536", "title": "重庆市巴蜀中学2024−2025学年上学期12月月考八年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "532315586325422080", "questionArticle": "<p>9．为响应“科教兴国”的战略号召，某学校计划成立创客实验室，现需购买航拍无人机和编程机器人，已知购买2架航拍无人机和3个编程机器人所需费用相同，购买4个航拍无人机和7个编程机器人共需3480元，设购买1架航拍无人机需<i>x</i>元，购买1个编程机器人需<i>y</i>元，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x=3y \\\\ 4x+7y=3480 \\end{cases}  $ B． $ \\begin{cases} 3x{ \\rm{ = } }2y \\\\ 4x{ { + } }7y{ \\rm{ = } }3480 \\end{cases}  $ C． $ \\begin{cases} 2x{ \\rm{ = } }3y \\\\ 7x{ { + } }4y{ \\rm{ = } }3480 \\end{cases}  $ D． $ \\begin{cases} 3x{ \\rm{ = } }2y \\\\ 7x{ { + } }4y{ \\rm{ = } }3480 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川渠中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-01-09", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532315579505483776", "questionFeatureName": "生活背景问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "532315579505483776", "title": "四川省达州市渠县中学2024−2025学年八年级上学期期末考试数学模拟测试题（二）", "paperCategory": 1}, {"id": "996775870861312", "title": "广东省深圳市宝安区2021年九年级二模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "531850740295311360", "questionArticle": "<p>10．学校为激励更多班级积极参与“分类适宜，垃圾逢春”活动，决定购买拖把和扫帚作为奖品，奖励给垃圾分类表现优异的班级．若购买3把拖把和2把扫帚共需80元，购买2把拖把和1把扫帚共需50元．</p><p>（1）请问拖把和扫帚每把各多少元？</p><p>（2）现准备购买拖把和扫帚共200把，且要求购买拖把的费用不低于购买扫帚的费用，所有购买的资金不超过2690元，问有几种购买方案，哪种方案最省钱？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江温州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-01-09", "keyPointIds": "16441|16490", "keyPointNames": "其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531850725107736576", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "531850725107736576", "title": "浙江省温州市2024−2025学年八年级上学期六校第二次学情检测数学试题", "paperCategory": 1}, {"id": "209697580485222400", "title": "新疆维吾尔自治区克拉玛依市白碱滩区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 225, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 225, "timestamp": "2025-07-01T02:27:19.700Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}