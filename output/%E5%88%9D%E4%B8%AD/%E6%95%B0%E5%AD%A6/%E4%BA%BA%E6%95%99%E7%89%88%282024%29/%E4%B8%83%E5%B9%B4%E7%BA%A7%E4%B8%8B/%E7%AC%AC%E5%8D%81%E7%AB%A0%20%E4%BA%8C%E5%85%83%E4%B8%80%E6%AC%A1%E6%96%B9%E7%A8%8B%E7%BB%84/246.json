{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 245, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "518569170876801024", "questionArticle": "<p>1．在方程组 $ \\begin{cases}xy=1,\\\\ x+2y=3,\\end{cases}\\begin{cases}x=1,\\\\ y=1,\\end{cases}\\begin{cases}\\dfrac{1}{x}+\\dfrac{1}{y}=1,\\\\ x+y=1,\\end{cases}\\begin{cases}x=2,\\\\ 3y-x=1,\\end{cases}\\begin{cases}x+y=5,\\\\ y=7+z\\end{cases} $ 中，是二元一次方程组的有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A. 2个B. 3个C. 4个D. 5个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2024-12-03", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317484809101312", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "544317484809101312", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.1 二元一次方程组的概念 课时练习", "paperCategory": 1}, {"id": null, "title": "第十章 二元一次方程组10.1 二元一次方程组的概念《2025春初中必刷题 数学七年级下册 RJ》", "paperCategory": 2}, {"id": "564578973704822784", "title": "河北省石家庄市第三十八中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "518916081706115072", "questionArticle": "<p>2．在“双十一”活动中，某淘宝店家上架300个 $ A $ 商品和240个 $ B $ 商品进行销售，已知购买2个 $ A $ 商品和3个 $ B $ 商品共需900元，购买1个 $ A $ 商品和2个 $ B $ 商品共需550元．</p><p>(1)求 $ A $ 商品和 $ B $ 商品的售价分别是多少元？</p><p>(2)在 $ A $ 商品售出总数量的 $ \\dfrac { 2 } { 3 } $ ， $ B $ 商品售出总数量的 $ \\dfrac { 3 } { 4 } $ 时，为了尽快回笼资金，店主决定对剩余的 $ A $ 商品每个打 $ a $ 折销售，对剩余的 $ B $ 商品每个降价 $ 5a $ 元销售，很快全部售完，若要保证销售总额不低于87600元，求 $ a $ 的最小值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市第一中学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-03", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "518916072239570944", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "518916072239570944", "title": "重庆市第一中学校2024−2025学年九年级上学期数学半期试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516370200024883200", "questionArticle": "<p>3．如图，三角形 $ ABC $ 经过某种变换后得到三角形 $ DEF $ ，点 $ \\mathrm{ A } $ 、 $ B $ 、 $ C $ 的对应点分别是点 $ D $ 、 $ E $ 、 $ F $ ，请观察它们之间的关系，完成以下问题：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/11/25/2/1/0/0/0/516370046949564430/images/img_15.png\" style=\"vertical-align:middle;\" width=\"282\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p><p>(1)请分别写出点<i>A</i>，<i>D</i>的坐标：<i>A</i><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，<i>D</i><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)若三角形 $ ABC $ 内任意一点<i>M</i>的坐标是 $ \\left ( { x,y } \\right )  $ ，点<i>M</i>经过这种变换后得到点<i>N</i>，点<i>N</i>的坐标是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(3)在上述变换情况下，点 $ P\\left ( { a+3,-b+6 } \\right )  $ 与点 $ Q\\left ( { 2b-3,-2a } \\right )  $ 为对应点，求 $ a+b $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江西南外 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-02", "keyPointIds": "16424|16497|16803", "keyPointNames": "加减消元法解二元一次方程组|点的坐标|关于原点对称的点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516370193133641728", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516370193133641728", "title": "江西省南昌市外国语学校教育集团2024−2025学年九年级上学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "516370340093665280", "questionArticle": "<p>4．我市公交公司为落实“绿色出行，低碳环保”的城市发展理念，计划购买<i>A</i>，<i>B</i>两种型号的新型公交车，已知购买1辆<i>A</i>型公交车和3辆<i>B</i>型公交车需要45万元，2辆<i>A</i>型公交车和1辆<i>B</i>型公交车需要35万元．</p><p>(1)求<i>A</i>型公交车和<i>B</i>型公交车每辆各多少万元？</p><p>(2)公交公司计划购买<i>A</i>型公交车和<i>B</i>型公交车共120辆，且购买<i>A</i>型公交车的总费用不高于<i>B</i>型公交车的总费用，那么该公司最多购买多少辆<i>A</i>型公交车？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁本溪 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-02", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516370332585861120", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516370332585861120", "title": "辽宁省本溪市2024−2025学年九年级上学期11月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516371777712332800", "questionArticle": "<p>5．“立冬时节寒风起，万木凋零百草枯，某家电力公司为了提高电力输送效率，在十月份对输电线路<i>A</i>和<i>B</i>进行了两次升级，来应对冬天的用电高峰．公司记录了两次升级工程的公里数和费用，如下表所示：（注：十月两次升级中每条线路每公里升级费用均不变）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">升级情况</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">线路<i>A</i>（公里）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">线路<i>B</i>（公里）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">总费用（万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">第一次升级</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">380</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">第二次升级</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">520</p></td></tr></table><p>(1)十月份，线路<i>A</i>和线路<i>B</i>每公里的升级费用各是多少万元？</p><p>(2)电力公司计划在十一月对这两条线路进行第三次升级．由于采用了新的材料，预计线路<i>A</i>每公里的升级费用比之前减少 $ 2a\\% $ ，线路<i>B</i>每公里的升级费用不变．线路<i>A</i>升级的公里数与第二次升级的公里数相同，线路<i>B</i>升级的公里数比第二次升级的公里数长 $ 3a $ 公里，若第三次升级总费用比第二次升级总费用多48万元，求<i>a</i>的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆南开 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-02", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516371766903611392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516371766903611392", "title": "重庆市南开中学校2024−2025学年八年级上学期11月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516371776659562496", "questionArticle": "<p>6．解二元一次方程组：</p><p>(1) $ \\begin{cases} x=3y \\\\ 4x+y=13 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 4\\left ( { x-1 } \\right ) +2y=y+8 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 2 }=\\dfrac { 11 } { 6 } \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆南开 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-02", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516371766903611392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516371766903611392", "title": "重庆市南开中学校2024−2025学年八年级上学期11月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516008162358501376", "questionArticle": "<p>7．我们知道，任意一个有理数与无理数的和为无理数，任意一个不为零的有理数与一个无理数的积为无理数，而零与无理数的积为零，由此可得：如果 $ ax+b=0 $ ，其中 $ a $ 、 $ b $ 为有理数， $ x $ 为无理数，那么 $ a=0 $ ，且 $ b=0 $ ，运用上述知识可解决下列问题：若 $ \\sqrt { 2 }a-b=3 $ ，其中 $ a $ 、 $ b $ 为有理数，那么 $ a=0 $ ，且 $ b=-3 $ ．</p><p>(1)如果 $ \\sqrt { 2 }\\left ( { a-2 } \\right ) +b=4 $ ，其中 $ a $ 、 $ b $ 为有理数，那么 $ a= $ _， $ b= $ _；</p><p>(2)如果 $ \\left ( { 1+\\sqrt { 7 } } \\right ) a-\\sqrt { 7 }b=5 $ ，其中 $ a $ 、 $ b $ 为有理数，求 $ ab $ 的算术平方根．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建泉州五中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-11-28", "keyPointIds": "16288|16423", "keyPointNames": "算术平方根|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516008155047829504", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516008155047829504", "title": "福建省泉州第五中学2024−2025学年八年级上学期11月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516747495487086592", "questionArticle": "<p>8．若 $ x{^{3m-8}}-2y{^{n-1}}=5 $ 是二元一次方程，则 $ mn= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024陕西实验中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-11-27", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516747489501814784", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "516747489501814784", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第二次质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "516747390855979008", "questionArticle": "<p>9．列二元一次方程组解决下面问题：为落实教育部门安排的学生社会实践活动，八年级（9）班开展了一次蔬菜售卖体验．其中第一小组花128元从蔬菜批发市场批发了豆角和土豆共 $ 55{ \\rm{ k } }{ \\rm{ g } } $ 到蔬菜市场去卖，豆角和土豆当天的批发价与零售价如表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 94.9pt;\"><p>品名</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>豆角</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>土豆</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 94.9pt;\"><p>批发价/（元/ $ { \\rm{ k } }{ \\rm{ g } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>2.4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>2.2</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 94.9pt;\"><p>零售价/（元/ $ { \\rm{ k } }{ \\rm{ g } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>3.8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>3.3</p></td></tr></table><p>该小组当天卖完这些豆角和土豆可赚多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西工大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-11-27", "keyPointIds": "16424|16440", "keyPointNames": "加减消元法解二元一次方程组|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516747383104905216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516747383104905216", "title": "陕西省西安市西北工业大学附属中学2024−2025学年上学期八年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516747390130364416", "questionArticle": "<p>10．计算：</p><p>(1) $ -1{^{4}}-\\left  | { \\sqrt { 3 }-1 } \\right  | +{\\left( { \\sqrt { 2 }-1.414 } \\right) ^ {0}}-{\\left( { -\\dfrac { 1 } { 2 } } \\right) ^ {-1}} $ </p><p>(2) $ \\sqrt { 48 }\\div \\sqrt { 3 }-\\sqrt { \\dfrac { 1 } { 2 } }\\times \\sqrt { 12 }+\\sqrt { 24 } $ </p><p>(3)解二元一次方程组： $ \\begin{cases} x-2y=4 \\\\ 4x+3y=5 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024陕西西工大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-11-27", "keyPointIds": "16323|16372|16389|16424", "keyPointNames": "零指数幂|负整数指数幂|二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516747383104905216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516747383104905216", "title": "陕西省西安市西北工业大学附属中学2024−2025学年上学期八年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 246, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 246, "timestamp": "2025-07-01T02:29:53.713Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}