{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 237, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "524717164319776768", "questionArticle": "<p>1．（1）计算： $ \\left  | { \\sqrt { 2 }-2 } \\right  | +2\\sqrt { 18 }-\\sqrt { 24 }\\times \\sqrt { \\dfrac { 1 } { 3 } } $ ．</p><p>（2）解二元一次方程组： $ \\begin{cases} 4x-2y+5=0 \\\\ 9x+3y=0 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024辽宁沈阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 2, "createTime": "2024-12-20", "keyPointIds": "16389|16424", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524717157722136576", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524717157722136576", "title": "辽宁省沈阳市第四十三中学2024−2025学年上学期八年级数学期末模拟试题", "paperCategory": 1}, {"id": "522522525844152320", "title": "辽宁省沈阳市第四十三中学 2024−2025学年上学期八年级12月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524719314512945152", "questionArticle": "<p>2．某机械林场经过三代务林人的持续奋斗，已知现在该林场的林木总蓄积比原来增加了1073万立方米：又知现在该林场的林木总蓄积比原来的35倍还多19万立方米，请问该林场原来和现在的林木总蓄积分别是多少万立方米？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 0, "createTime": "2024-12-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [], "questionTypeCode": "6"}, {"questionId": "524719314051571712", "questionArticle": "<p>3．解方程组：</p><p>(1) $ \\begin{cases} 4\\left ( { x-1 } \\right ) -3\\left ( { y+2 } \\right ) =-8 \\\\ 3x-2y=3 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x+4y } { 3 }-6=\\dfrac { 5y-1 } { 2 } \\\\ 5x+7y=9 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 0, "createTime": "2024-12-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [], "questionTypeCode": "6"}, {"questionId": "524718390931398656", "questionArticle": "<p>4．若式子 $ \\dfrac { 1 } { x-3 } $ 有意义，则实数<i>x</i>的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-19", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718383402622976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718383402622976", "title": "陕西省西安市高新第一学校2024−2025学年上学期八年级数学第二次月考试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524718390541328384", "questionArticle": "<p>5．【数学问题】解方程组 $ \\begin{cases} x+y=2 \\\\ 5x-2\\left ( { x+y } \\right ) =6 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718383402622976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718383402622976", "title": "陕西省西安市高新第一学校2024−2025学年上学期八年级数学第二次月考试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524718390205784064", "questionArticle": "<p>6．《九章算术》中记载：“今有共买羊，人出五，不足四十五；人出七，不足三，问人数、羊价各几何？”其大意是：今有人合伙买羊，若每人出5钱，还差45钱；若每人出7钱，还差3钱，问合伙人数、羊价各是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718383402622976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718383402622976", "title": "陕西省西安市高新第一学校2024−2025学年上学期八年级数学第二次月考试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524718389715050496", "questionArticle": "<p>7．解方程组：</p><p>(1) $ \\begin{cases} 2x=3-y \\\\ 3x+2y=2 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3y-2x=17 \\\\ 4x+2y=6 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-19", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718383402622976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718383402622976", "title": "陕西省西安市高新第一学校2024−2025学年上学期八年级数学第二次月考试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524718387219439616", "questionArticle": "<p>8．下列方程组是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x-2y=0 \\\\ 4x-1=y \\end{cases}  $　　　　B． $ \\begin{cases} x+y=5 \\\\ y+z=3 \\end{cases}  $　　　　C． $ \\begin{cases} x-y=20 \\\\ xy-1=0 \\end{cases}  $　　　　D． $ \\begin{cases} x=2y+1 \\\\ x+\\dfrac { 1 } { y }=2 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024陕西高新一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-19", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718383402622976", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718383402622976", "title": "陕西省西安市高新第一学校2024−2025学年上学期八年级数学第二次月考试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524718151310811136", "questionArticle": "<p>9．小美打算在“母亲节”买一束百合和康乃馨组合的鲜花送给妈妈．已知买2支百合和1支康乃馨共需花费14元，3支康乃馨的价格比2支百合的价格多2元．</p><p>（1）求买一支康乃馨和一支百合各需多少元？</p><p>（2）小美准备买康乃馨和百合共11支，且康乃馨不多于9支，设买康乃馨<i>x</i>支，买这束鲜花所需总费用为<i>w</i>元．</p><p>①求<i>w</i>与<i>x</i>之间的函数关系式；</p><p>②请你帮小美设计一种使费用最少的买花方案，并求出最少费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东济南 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 2, "createTime": "2024-12-19", "keyPointIds": "16434|16543", "keyPointNames": "方案问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718141651329024", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718141651329024", "title": "山东省济南市高新区五校联考2024−2025学年八年级上学期月考数学试题", "paperCategory": 1}, {"id": "152072857589161984", "title": "河南省郑州市第三中学2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524718150581002240", "questionArticle": "<p>10．解下列方程组：</p><p>（1） $ \\begin{cases} x-y=4 \\\\ 2x+y=5 \\end{cases}  $  </p><p>（2） $ \\begin{cases} 3x+2y=8 \\\\ 4x-5y=3 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|640000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东济南 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2024-12-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524718141651329024", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524718141651329024", "title": "山东省济南市高新区五校联考2024−2025学年八年级上学期月考数学试题", "paperCategory": 1}, {"id": "151743216106119168", "title": "宁夏回族自治区银川市第十中学2021-2022学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 238, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 238, "timestamp": "2025-07-01T02:28:57.011Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}