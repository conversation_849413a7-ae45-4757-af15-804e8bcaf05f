{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 201, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544318162713485312", "questionArticle": "<p>1．已知关于 $ x $ , $ y $ 的方程组 $ \\begin{cases}x+y-5m=0,\\\\ x-y-9m=0\\end{cases} $ 的解也是方程 $ 2x+3y=6 $ 的解，则 $ 4m-5 $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2 $ B．2C． $ -1 $ D．1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318162449244160", "questionArticle": "<p>2．以方程组 $ \\begin{cases}y=-x+2,\\\\ y=x-1\\end{cases} $ 的解为坐标的点到 $ y $ 轴的距离是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1.5B．2C．2.5D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423|16497", "keyPointNames": "代入消元法解二元一次方程组|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318162185003008", "questionArticle": "<p>3．解三元一次方程组 $ \\begin{cases}3x-4y=1,\\mathrm{①}\\\\ 4x+6y-z=2,\\mathrm{②}\\\\ 13x-5y+2z=4\\mathrm{③}\\end{cases} $ 时，要使解法较为简便，应（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．先消去 $ x $ B．先消去 $ y $ C．先消去 $ z $ D．先消去常数</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318161924956160", "questionArticle": "<p>4．小明解得方程组 $ \\begin{cases}3x+y=\\mathrm{●},\\\\ 3x-y=10\\end{cases} $ 的解为 $ \\begin{cases}x=2,\\\\ y=★,\\end{cases} $ 由于不小心滴上了两滴墨水，刚好盖住了两个数●和★，则这两个数分别为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．10和4B．2和 $ -4 $ C． $ -2 $ 和4D． $ -2 $ 和 $ -4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318161656520704", "questionArticle": "<p>5．若方程 $ (m-2){x}^{|m|-1}+(n+3){y}^{{n}^{2}-8}=5 $ 是关于 $ x $ , $ y $ 的二元一次方程，则（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}m=2,\\\\ n=3\\end{cases} $ B． $ \\begin{cases}m=-2,\\\\ n=-3\\end{cases} $ </p><p>C． $ \\begin{cases}m=2,\\\\ n=-3\\end{cases} $ D． $ \\begin{cases}m=-2,\\\\ n=3\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544318161329364992", "questionArticle": "<p>6．若 $ \\begin{cases}x=3,\\\\ y=2\\end{cases} $ 是二元一次方程 $ x-my=1 $ 的解，则 $ m $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B． $ \\dfrac{1}{2} $ C． $ -1 $ D． $ -\\dfrac{1}{2} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544318160008159232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544318160008159232", "title": "2024—2025学年七年级下册人教版（2024）数学第十章二元一次方程组 单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "542803164371656704", "questionArticle": "<p>7．为更好地满足本地市民和外地游客的消费需求，岳阳某超市在“春节”黄金周前投入11220元资金购进甲、乙两种水果共400箱，这两种水果的成本价和标价如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.95pt;\"><p style=\"text-align:center;\">类别/单价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">成本价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">标价（元/箱）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.95pt;\"><p style=\"text-align:center;\">甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">24</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.95pt;\"><p style=\"text-align:center;\">乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">33</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">50</p></td></tr></table><p>(1)该超市购进甲、乙两种水果各多少箱？</p><p>(2)为了促销，该超市将甲种水果按成本价提高50%后标价销售；乙种水果以标价的8折销售．若这400箱水果在“春节”黄金周结束后全部售完，则该超市可获得利润多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南岳阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16437|28548", "keyPointNames": "销售利润问题|有理数混合运算的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542803155270017024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542803155270017024", "title": "湖南省岳阳市2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542803163583127552", "questionArticle": "<p>8．解方程（组）：</p><p>(1) $ 6x-2=x+8 $ ；</p><p>(2) $ \\begin{cases} 2x+5y=8 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 2 }=2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南岳阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542803155270017024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542803155270017024", "title": "湖南省岳阳市2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543968011490729984", "questionArticle": "<p>9．某校准备组织七年级师生去红军长征湘江战役纪念馆参观学习，学校联系某客运公司有60座和45座两种客车可供租用．学校如果全部租用45座的客车，那么七年级师生全部有座，且还剩余15个空座位；如果全部租用60座的客车，则可少租3辆，且正好坐满．</p><p>(1)求七年级师生的总人数；</p><p>(2)已知客运公司60座的客车每辆每天的租金是900元，45座的客车每辆每天的租金是700元．若学校从该客运公司租用客车，要使每位师生都有座位，且每辆客车都恰好坐满，求出满足条件的所有租车方案，并说明哪一种租车方案最省钱？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西桂林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16412|16420|16434", "keyPointNames": "和差倍分问题|二元一次方程的解|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968003827736576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "543968003827736576", "title": "广西桂林市2024—2025学年上学期七年级数学期末试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "543968010635091968", "questionArticle": "<p>10．解二元一次方程组： $ \\begin{cases} x-2y=6 \\\\ 2x+y=7 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西桂林 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "543968003827736576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "543968003827736576", "title": "广西桂林市2024—2025学年上学期七年级数学期末试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 202, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 202, "timestamp": "2025-07-01T02:24:42.780Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}