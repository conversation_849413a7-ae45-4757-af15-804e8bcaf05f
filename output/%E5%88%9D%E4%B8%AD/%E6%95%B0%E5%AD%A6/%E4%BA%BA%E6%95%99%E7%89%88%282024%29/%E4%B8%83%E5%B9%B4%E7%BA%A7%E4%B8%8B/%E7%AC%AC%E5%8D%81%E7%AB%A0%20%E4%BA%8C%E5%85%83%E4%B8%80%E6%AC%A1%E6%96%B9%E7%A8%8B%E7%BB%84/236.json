{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 235, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "524199706282467328", "questionArticle": "<p>1．《算法统宗》中有一道题为“隔沟计算”，其原文是：甲乙隔沟放牧，二人暗里参详，多你一倍之上；乙说得甲九只羊，两人闲坐恼心肠，画地算了半晌．这个题目的意思是：甲、乙两个牧人隔着山沟放羊，甲对乙说：“我若得你9只羊，我的羊多你一倍．”乙对甲说：“我若得你9只羊，我们两家的羊数就一样多．” 设甲有<i>x</i>只羊，乙有<i>y</i>只羊，根据题意列出二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-9=2\\left ( { y+9 } \\right )  \\\\ y+9=x-9 \\end{cases}  $ B． $ \\begin{cases} x+9=2\\left ( { y-9 } \\right )  \\\\ y+9=x-9 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+9=2y \\\\ y+9=x \\end{cases}  $ D． $ \\begin{cases} x-9=2y \\\\ y+9=x-9 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|140000|210000|450000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西运城 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 5, "createTime": "2024-12-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524199700175560704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524199700175560704", "title": "山西省运城市盐湖区实验中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "493852063371141120", "title": "广西南宁市青秀区三美学校2024−2025学年八年级上学期开学数学试题", "paperCategory": 1}, {"id": "473972092804833280", "title": "湖南省长沙市华益中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "402647767561052160", "title": "辽宁省沈阳市沈北新区2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "203125975512555520", "title": "江苏省常州市教育学会2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524714286452613120", "questionArticle": "<p>2．为落实立德树人的根本任务，培养有理想、有本领、有担当的新时代好少年，某校组织八年级师生开展以“寻根河南  生生不息”为主题，为期一天的“只有河南之旅”研学实践活动，学校计划租用甲、乙两种不同型号的客车，已知2辆甲型客车和3辆乙型客车可乘坐270人，3辆甲型客车和2辆乙型客车可乘坐255人．</p><p>(1)甲、乙两种不同型号的客车每辆分别可乘坐多少人？</p><p>(2)已知甲型客车每天的租车费用为1200元，乙型客车每天的租车费用为1500元，学校计划共租用12辆客车，请写出总租车费用 $ w $ （元）与租用甲型客车数量 $ a $ （辆）的函数关系式；</p><p>(3)如果客车租赁公司的甲型客车只剩下8辆，乙型客车还有很多．在（2）的条件下，请选出最省钱的租车方案，并求出最少租车费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南驻马店市第二初级中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-22", "keyPointIds": "16435|16543", "keyPointNames": "分配问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524714278265331712", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524714278265331712", "title": "河南省驻马店市第二初级中学2024-2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524714285458563072", "questionArticle": "<p>3．已知 $ a，b，c $ 满足 $ \\sqrt { 2a+b-4 }+\\left  | { a+1 } \\right  | =\\sqrt { b-c }+\\sqrt { c-b } $ ，求 $ -10a-b+2c $ 的算术平方根．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南驻马店市第二初级中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2024-12-22", "keyPointIds": "16288|16378|16424", "keyPointNames": "算术平方根|二次根式有意义的条件|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524714278265331712", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524714278265331712", "title": "河南省驻马店市第二初级中学2024-2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524714284732948480", "questionArticle": "<p>4．若二元一次方程组 $ \\begin{cases} x+2y=m+3 \\\\ x+y=2m \\end{cases}  $ 的解 $ x $ ， $ y $ 的值恰好是一个等腰三角形两边的长，且这个等腰三角形的周长为7，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南驻马店市第二初级中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-22", "keyPointIds": "16420|16640|16661", "keyPointNames": "二元一次方程的解|三角形三边关系|等腰三角形的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524714278265331712", "questionMethodName": "分类讨论思想", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524714278265331712", "title": "河南省驻马店市第二初级中学2024-2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524714282287669248", "questionArticle": "<p>5．在解关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} \\left ( { m+ } \\right ) 1x-ny=8① \\\\ nx+my=11② \\end{cases}  $ 时，可以用 $ ①\\times 2+② $ 消去未知数<i>x</i>，也可以用 $ ①+②\\times 5 $ 消去未知数<i>y</i>，则 $ m-n= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4　　　　B． $ -\\dfrac { 8 } { 3 } $　　　　C． $ -\\dfrac { { { 6 } } } { { { 7 } } } $　　　　D． $ \\dfrac { 8 } { 7 } $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南驻马店市第二初级中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524714278265331712", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "524714278265331712", "title": "河南省驻马店市第二初级中学2024-2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524354239558098944", "questionArticle": "<p>6．一水果经营户从水果批发市场批发水果进行零售，部分水果批发价格与 零售价格如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 99pt;\"><p>水果品种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>梨子</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>菠萝</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>苹果</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>车厘子</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 99pt;\"><p>批发价格(元/ $ { { &nbsp; } }{ \\rm{ k } }{ \\rm{ g } } $  )</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 4 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 5 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 6 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p> $ 40 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 99pt;\"><p>零售价格(元/  $ { \\rm{ k } }{ \\rm{ g } } $  )</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 5 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 6 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 8 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p> $ 50 $ </p></td></tr></table><p>请解答下列问题：</p><p>(1)第一天，该经营户用 $ 5200 $ 元批发了车厘子和苹果共 $ 300{ \\rm{ k } }{ \\rm{ g } } $ &nbsp;&nbsp;，当日全部售出，求这两种水果获得的总利润？</p><p>(2)第二天，该经营户依然用 $ 5200 $ 元批发了车厘子和苹果，当日销售结束清点盘存时发现进货单丢失，只记得这两种水果的批发量均为正整数且车厘子的进货量不低于 $ 97{ \\rm{ k } }{ \\rm{ g } } $ ，这两种水果已全部售出且总利润高于第一天这两种水果的总利润，请通过计算说明该经营户第二天批发 这两种水果可能的方案有哪些？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东青岛 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2024-12-21", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589939913774112768", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "589939913774112768", "title": "山东省青岛市2024−2025学年下学期第三次月考八年级数学试卷", "paperCategory": 1}, {"id": "524354231261765632", "title": "广西壮族自治区南宁市第二中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "524354357833277440", "questionArticle": "<p>7．《九章算术》中有这样一道题，大意是：假设有5头牛、2只羊，值10两金；2头牛、5只羊，值8两金．问1头牛、1只羊各值多少金？设1头牛、1只羊分别值 $ x $ ， $ y $ 金，则列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+y=10, \\\\ x+5y=8 \\end{cases}  $　　　　B． $ \\begin{cases} 5x+2y=10, \\\\ 2x+5y=8 \\end{cases}  $　　　　C． $ \\begin{cases} 5x+y=8, \\\\ x+5y=10 \\end{cases}  $　　　　D． $ \\begin{cases} 5x+2y=8, \\\\ 2x+5y=10 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024贵州贵阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524354351999000576", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "524354351999000576", "title": "贵州省贵阳市2024−2025学年九年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "524353074338504704", "questionArticle": "<p>8．新农村建设工地需派96名工人去挖土或运土，平均每人每天挖土 $ 5{ \\rm{ m } }{^{3}} $ 或运土 $ 3{ \\rm{ m } }{^{3}} $ ．如何分配挖土和运土的人数，使得挖出的土刚好能被运完？若设分配 $ x $ 人挖土， $ y $ 人运土．为求 $ x $ ， $ y $ ，小聪正确地列出了其中一个方程 $ x+y=96 $ ，你所列的另一个方程为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024浙江绍兴 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-21", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524353067673755648", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524353067673755648", "title": "浙江省绍兴市越城区元培中学2023−2024学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524352951336345600", "questionArticle": "<p>9．若数<i>a</i>既使得关于 $ x、y $ 的二元一次方程组 $ \\begin{cases} x+y=6 \\\\ 3x-2y=a+3 \\end{cases}  $ 有正整数解，又使得关于<i>x</i>的不等式组 $ \\begin{cases} \\dfrac { 3x-5 } { 2 } &gt; x+a \\\\ \\dfrac { 3-2x } { 9 }\\leqslant  -3 \\end{cases}  $ 的解集为 $ x\\geqslant  15 $ ，那么所有满足条件的<i>a</i>的值之和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江宁波 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-21", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524352943191007232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "524352943191007232", "title": "浙江省宁波市鄞州区第七中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "524352859388813312", "questionArticle": "<p>10．某市为助力新能源汽车产业的健康发展，打造新能源交通生态城市，近几年在全市范围内安装电动汽车充电桩．2021年该市投入资金1250万元，安装<i>A</i>型充电桩200个和<i>B</i>型充电桩300个；2022年又投入2000万元，安装<i>A</i>型充电桩250个和<i>B</i>型充电桩500个．已知这两年安装<i>A</i>、<i>B</i>两种型号的充电桩单价不变．</p><p>(1)求安装<i>A</i>型充电桩和<i>B</i>型充电桩的单价各是多少万元？</p><p>(2)为适应电动汽车快速发展的需要，市政府计划2023年再安装<i>A</i>、<i>B</i>两种型号的充电桩共200个．考虑到充电容量等综合因素，决定安装<i>A</i>型充电桩的数量不多于<i>B</i>型充电桩的一半．在安装单价不变的前提下，当安装<i>A</i>型充电桩多少个时，所需投入的总费用最少，最少费用是多少万元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江宁波 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-21", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "524352850706604032", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "524352850706604032", "title": "浙江省宁波市海曙区第十五中学2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 236, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 236, "timestamp": "2025-07-01T02:28:41.644Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}