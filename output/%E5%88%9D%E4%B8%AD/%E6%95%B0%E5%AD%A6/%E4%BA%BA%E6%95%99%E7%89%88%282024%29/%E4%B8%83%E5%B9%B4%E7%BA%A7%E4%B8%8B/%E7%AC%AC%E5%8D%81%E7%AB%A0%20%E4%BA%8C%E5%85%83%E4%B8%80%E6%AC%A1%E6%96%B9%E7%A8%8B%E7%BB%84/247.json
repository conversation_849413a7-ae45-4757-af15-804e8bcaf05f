{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 246, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "516747389668990976", "questionArticle": "<p>1．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 4x+my=12 \\\\ \\left ( { m+n } \\right ) x-2y=6 \\end{cases}  $ 有无数组解，其中<i>m</i>、<i>n</i>不为0，则 $ mn= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024陕西西工大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-11-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516747383104905216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "516747383104905216", "title": "陕西省西安市西北工业大学附属中学2024−2025学年上学期八年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "514817493820547072", "questionArticle": "<p>2．解方程组： $ \\begin{cases} 3x+4y=2 \\\\ 2x-y=5 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|350000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建厦门六中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 6, "createTime": "2024-11-27", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "514817486530846720", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "514817486530846720", "title": "福建省厦门第六中学2024−2025学年八年级上学期期中质量检测数学试卷", "paperCategory": 1}, {"id": "461314907755028480", "title": "湖南省株洲市醴陵市来龙门街道中心学校2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "448975190480953344", "title": "北京市第一五六中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "311233085751730176", "title": "湖南省长沙市南雅中学2022-2023学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "186846490614079488", "title": "北京市北京文汇中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "202440442155147264", "title": "福建省福州市台江区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516747388943376384", "questionArticle": "<p>3．若 $ \\left ( { a-2 } \\right ) x{^{\\left  | { a } \\right  | -1}}+3y=1 $ 是关于 $ x、y $ 的二元一次方程，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|230000|110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西工大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 3, "createTime": "2024-11-27", "keyPointIds": "16258|16419", "keyPointNames": "绝对值方程|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516747383104905216", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "516747383104905216", "title": "陕西省西安市西北工业大学附属中学2024−2025学年上学期八年级数学期中考试卷", "paperCategory": 1}, {"id": "444986477182754816", "title": "北京市西城区北京市第八中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "299649331673473024", "title": "黑龙江省哈尔滨市萧红中学2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "516747493675147264", "questionArticle": "<p>4．下列各方程中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x-\\dfrac { 2 } { y }=5 $</p><p>B． $ 3x+2y=5+2y $</p><p>C． $ x=y{^{2}}+1 $</p><p>D． $ 2y=3x-4 $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000|610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西实验中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2024-11-27", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516747489501814784", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "516747489501814784", "title": "陕西省咸阳市实验中学2024−2025学年八年级上学期第二次质量检测数学试卷", "paperCategory": 1}, {"id": "196619899984191488", "title": "江西省宜春市宜丰中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "516381857027497984", "questionArticle": "<p>5．将15个编号为1~15的小球全部放入甲、乙、丙三个盘子内，每个盘子里的小球不少于4个，甲盘中小球编号的平均值为3．</p><p>（1）写出一种甲盘中小球的编号是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若乙、丙盘中小球编号的平均值分别为8，13，则乙盘中小球的个数可以是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024北京22中等校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-11-26", "keyPointIds": "16444|16881", "keyPointNames": "三元一次方程组的应用|算术平均数", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516381849700048896", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "516381849700048896", "title": "北京市第二十二中学、二十一中学联盟校2024−2025学年九年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "516379073926438912", "questionArticle": "<p>6．已知当 $ x=1 $ 时， $ 3ax{^{3}}+bx{^{2}}-2cx+4=8 $ 且 $ ax{^{3}}+2bx{^{2}}-cx-15=-14 $ ，则当 $ x=-1 $ 时， $ 5ax{^{3}}-5bx{^{2}}-4cx+2024= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2017 $　　　　B． $ 2018 $　　　　C． $ 2019 $　　　　D． $ 2020 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽芜湖 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-11-26", "keyPointIds": "16305|16443", "keyPointNames": "代数式求值|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516379069165903872", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "516379069165903872", "title": "安徽省芜湖市第二十九中学2024−2025学年七年级上学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "516377521060880384", "questionArticle": "<p>7．某厂为了提高生产力，计划新购置 $ {\\rm \\mathit{A},} B $ 两种型号的生产设备共 $ 12 $ 台．已知<i>A</i>型每台 $ a $ 元，每月可以生产 $ 220 $ 吨产品； $ B $ 型每台 $ b $ 元，每月可以生产 $ 180 $ 吨产品．购买一台<i>A</i>型设备比购买一台 $ B $ 型设备多 $ 3 $ 万元，则买 $ 1 $ 台<i>A</i>型设备比购买 $ 3 $ 台 $ B $ 型设备少 $ 3 $ 万元．根据以上信息，解答下列问题：</p><p>(1)求出 $ a $ , $ b $ 的值．</p><p>(2)若计划购置总费用不超过 $ 50 $ 万元，且两种型号设备都要购买，该厂有哪些购买方案？</p><p>(3)在（2）的条件下，若每月生产产品不得低于 $ 2260 $ 吨，为了节约资金，请你为该厂设计一种最省钱的购买方案．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江金华 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-11-26", "keyPointIds": "16434|16490", "keyPointNames": "方案问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516377512655495168", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "516377512655495168", "title": "浙江省金华市东阳市江北五校联考2024−2025学年八年级上学期11月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "516006175378284544", "questionArticle": "<p>8．已知 $ a=\\sqrt[x-y] { 2x+5y+7 } $ 是  $ 2x+5y+7 $ 的算术平方根， $ b=\\sqrt[x-2y+3] { 5x-42y } $ 是 $ 5x-42y $ 的立方根，试求 $ b-a $ 的立方根．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南周口 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2024-11-25", "keyPointIds": "16288|16290|16424", "keyPointNames": "算术平方根|立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "516006169476898816", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "516006169476898816", "title": "河南省周口市项城市南顿第三中学等校2024−2025学年八年级上学期11月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "514201654809370624", "questionArticle": "<p>9．市中心的一家时尚咖啡店推出了两款新颖的特色饮品，一款是“陨石拿铁”另一款是“摘星摩卡”．已知2杯“陨石拿铁”和5杯“摘星摩卡”总售价为240元；3杯“陨石拿铁”和4杯“摘星摩卡”总售价为234元．</p><p>(1)求“陨石拿铁”和“摘星摩卡”各自的单价；</p><p>(2)咖啡豆是制作咖啡饮品的主要原料之一，咖啡店老板发现今年第三季度平均每千克咖啡豆的价格比第二季度上涨了 $ 25\\% $ ，第三季度花 $ 6000 $ 元买到的咖啡豆数量比第二季度花同样的钱买到的咖啡豆数量少了12千克，求第三季度咖啡豆的单价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆八中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-11-24", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "514201644856287232", "questionMethodName": "函数与方程思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "514201644856287232", "title": "重庆市第八中学2024−2025学年九年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "514820997838577664", "questionArticle": "<p>10．解下列方程组或不等式组：</p><p>(1) $ \\begin{cases} 3x+2y=19 \\\\ 2x-y=1 \\end{cases}  $ </p><p>(2) $ \\begin{cases} x &gt; \\dfrac { x+2 } { 3 } \\\\ 5x-3  &lt;  5+x \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏泰州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-11-23", "keyPointIds": "16423|16424|16489", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "514820989911343104", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "514820989911343104", "title": "江苏省泰州市靖江市八校联盟2024−2025学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 247, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 247, "timestamp": "2025-07-01T02:30:01.199Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}