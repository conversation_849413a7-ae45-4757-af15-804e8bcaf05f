{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 217, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "535215965102972928", "questionArticle": "<p>1．某商场购进了 $ A $ ， $ B $ 两种商品，若销售 $ 10 $ 件 $ A $ 商品和 $ 20 $ 件 $ B $ 商品，则可获利 $ 280 $ 元；若销售 $ 20 $ 件 $ A $ 商品和 $ 30 $ 件 $ B $ 商品，则可获利 $ 480 $ 元．</p><p>(1)求 $ A $ ， $ B $ 两种商品每件的利润；</p><p>(2)已知 $ A $ 商品的进价为 $ 24 $ 元 $ / $ 件，目前每星期可卖出 $ 200 $ 件 $ A $ 商品，市场调查反映：如调整 $ A $ 商品价格，每降价 $ 1 $ 元，每星期可多卖出 $ 20 $ 件，如何定价才能使 $ A $ 商品的利润最大？最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东枣庄 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-20", "keyPointIds": "16437|16565", "keyPointNames": "销售利润问题|销售问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "535215957263818752", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "535215957263818752", "title": "2024年山东省枣庄市部分中学中考数学一模试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "533728162531811328", "questionArticle": "<p>2．2023年中国新能源汽车市场火爆．某汽车隹售公司为抢占先机，计划购进一批新能源汽车进行销售．据了解，1辆 $ \\mathrm{ A } $ 型新能源汽车、3辆 $ B $ 型新能源汽车的进价共计55万元；4辆 $ \\mathrm{ A } $ 型新能源汽车、2辆 $ B $ 型新能源汽车的进价共计120万元．</p><p>(1)求 $ A，B $ 型新能源汽车每辆进价分别是多少万元．</p><p>(2)公司决定购买以上两种新能源汽车共100辆，该公司最多购买 $ \\mathrm{ A } $ 型车12辆，该汽车销售公司销售1辆 $ \\mathrm{ A } $ 型新能源汽车可获利0.9万元，销售1辆 $ B $ 型新能源汽车可获利0.4万元，若汽车全部销售完毕，那么销售 $ \\mathrm{ A } $ 型新能源汽车多少辆时获利最大？最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东济南稼轩初级中学  · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-19", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "533728153467920384", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "533728153467920384", "title": "山东省济南市稼轩学校2024—2025学年上学期八年级12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "533728161470652416", "questionArticle": "<p>3．解方程组</p><p>(1) $ \\begin{cases} 2x+y=7 \\\\ x-y=2 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x+2y=4 \\\\ \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=1 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024山东济南稼轩初级中学  · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-01-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "533728153467920384", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "533728153467920384", "title": "山东省济南市稼轩学校2024—2025学年上学期八年级12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "533728160417882112", "questionArticle": "<p>4．已知关于 $ x，y $ 的方程组 $ \\begin{cases} 2x+y=3k+2 \\\\ 4x-3y=-k+5 \\end{cases}  $ ，若 $ x-2y=1 $ ，则<i>k</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024山东济南稼轩初级中学  · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-19", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "533728153467920384", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "533728153467920384", "title": "山东省济南市稼轩学校2024—2025学年上学期八年级12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "534870248392007680", "questionArticle": "<p>5．为丰富学生的校园生活，某校计划购买一批跳绳和毽子供学生体育运动使用，已知购买1根跳绳和2个毽子共需35元，购买2根跳绳和3个毽子共需65元．</p><p>(1)跳绳和键子的单价分别是多少元？</p><p>(2)若学校购买跳绳和毽子共100件，且购买这批体育用品的总费用不超过2100元，则最多能购买多少根跳绳？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024广东梅州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-19", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534870236975112192", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "534870236975112192", "title": "2024年广东省梅州市部分学校中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534868626614034432", "questionArticle": "<p>6．太阳能是一种新型能源．某小区居民安装了甲、乙两种太阳能板进行发电．已知2片甲种太阳能板和1片乙种太阳能板一天共发电280度；1片甲种太阳能板和2片乙种太阳能板一天共发电260度．求甲、乙两种太阳能板每片每天的发电量．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林白山 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-18", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534868618825211904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534868618825211904", "title": "2024年吉林省白山市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "534868203987574784", "questionArticle": "<p>7．古代算书《四元玉鉴》中有“两果问价”问题：“九百九十九文钱，甜果苦果买一千，甜果九个十一文钱，苦果七个四文钱．试问甜苦果几个？”该问题意思是：九百九十九文钱买了甜果和苦果共一千个，已知十一文钱可买九个甜果，四文钱可买七个苦果，那么甜果、苦果各买了多少个？设甜果买了<i>x</i>个，苦果买了<i>y</i>个，根据题意，可列方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 11 } { 9 }x+\\dfrac { 4 } { 7 }y=999 \\end{cases}  $ B． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 9 } { 11 }x+\\dfrac { 7 } { 4 }y=999 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=999 \\\\ 11x+4y=1000 \\end{cases}  $ D． $ \\begin{cases} x+y=999 \\\\ 9x+7y=1000 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江绍兴 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-01-18", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534868198522396672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "534868198522396672", "title": "2024年4月浙江省绍兴一模 数学", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "534495340105342976", "questionArticle": "<p>8．方程组 $ \\begin{cases}3x+y=5,\\\\ x+3y=7\\end{cases} $ 的解为<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2023河南 · 中考真题", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-01-17", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "卷2 2023年河南省普通高中招生考试【2025中考必刷卷42套 数学 河南专用】", "paperCategory": 2}], "questionTypeCode": "5"}, {"questionId": "534497525803294720", "questionArticle": "<p>9．洛邑古城,被誉为“中原渡口”,截至目前景区总接待游客量突破2 600万人次,日接待游客量最高突破10万人次,是集游、玩、吃、住、购于一体的综合性人文旅游观光区,近期被大数据评为“第一热门汉服打卡地”.洛邑古城内某商铺打算购进A,B两种文创饰品对游客销售.若该商铺采购9件A种和6件B种共需330元;若采购5件A种和3件B种共需175元.两种饰品的售价均为每件30元.</p><p>(1)求A,B饰品每件的进价分别为多少元.</p><p>(2)该商铺计划采购这两种饰品共400件进行销售,其中A种饰品的数量不少于150件,且不大于300件.实际销售时,若A种饰品的数量超过250件时,则超出部分每件降价3元销售.</p><p>①求该商铺售完这两种饰品获得的利润<i>y</i>(元)与购进A种饰品的数量<i>x</i>(件)之间的函数关系式,并写出<i>x</i>的取值范围;</p><p>②设计能让这次采购的饰品获利最大的方案,并求出最大利润.</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河南 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-01-17", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "卷17 2024年河南省洛阳市中招模拟考试(二) 【2025中考必刷卷42套 数学 河南专用】", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "535216370625060864", "questionArticle": "<p>10．已知<i>x</i>，<i>y</i>满足方程组 $ \\begin{cases} x+m=4 \\\\ y-5=m \\end{cases}  $ ，则无论<i>m</i>取何值，<i>x</i>，<i>y</i>恒有关系式是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+y=1 $  B． $ x+y=-1 $  C． $ x+y=9 $  D． $ x-y=9 $  </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东枣庄 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-01-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "535216364593651712", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "535216364593651712", "title": "2024年山东省枣庄市第十五中学中考数学一模试卷", "paperCategory": 1}, {"id": "433036530703179776", "title": "2024年山东省枣庄市滕州市九年级第一次模拟数学模拟试题", "paperCategory": 1}, {"id": "201759339782119424", "title": "湖南省长沙市雨花区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 218, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 218, "timestamp": "2025-07-01T02:26:29.275Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}