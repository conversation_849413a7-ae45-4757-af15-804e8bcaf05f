{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 243, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "521444944537493504", "questionArticle": "<p>1．某品牌空调销售公司销售 $ \\mathrm{ A } $ 、 $ B $ 两种型号的空调．该公司为了提高销售人员的积极性，制定了新的工资方案．方案规定：个人工资 $ = $ 基本工资 $ + $ 奖励工资．每位销售人员的基本工资是4000元，月销售定额为6万元．在销售定额内，只得基本工资4000元；超过销售定额，超过部分的销售额按相应比例作为奖励工资，如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 74.65pt;\"><p style=\"text-align:center;\">奖励工资档次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 173.7pt;\"><p style=\"text-align:center;\">销售额</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 179pt;\"><p style=\"text-align:center;\">奖励工资占超过销售定额部分的比例</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 74.65pt;\"><p style=\"text-align:center;\">第一档</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 173.7pt;\"><p style=\"text-align:center;\">超过6万元但不超过8万元的部分</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 179pt;\"><p style=\"text-align:center;\"> $ 8{ \\rm{ \\% } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 74.65pt;\"><p style=\"text-align:center;\">第二档</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 173.7pt;\"><p style=\"text-align:center;\">超过8万元但不超过10万元的部分</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 179pt;\"><p style=\"text-align:center;\"> $ 10{ \\rm{ \\% } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 74.65pt;\"><p style=\"text-align:center;\">第三档</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 173.7pt;\"><p style=\"text-align:center;\">10万元以上的部分</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 179pt;\"><p style=\"text-align:center;\"> $ 12{ \\rm{ \\% } } $ </p></td></tr></table><p>(1)某销售人员希望每个月至少要领取6000元的个人工资，则该销售人员每月的销售额至少为多少元？</p><p>(2)该空调销售公司，5月份售出15台 $ \\mathrm{ A } $ 型空调和30台 $ B $ 型空调，销售额为18万元；6月份以同样的价格售出30台 $ \\mathrm{ A } $ 型空调和40台 $ B $ 型空调，销售额为30万元．7月销售员小李以同样的价格销售 $ \\mathrm{ A } $ 、 $ B $ 两种型号的空调共25台，得到的个人工资为8200元．请问7月销售员小李销售 $ \\mathrm{ A } $ 、 $ B $ 两种型号的空调各多少台？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市育才中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2024-12-10", "keyPointIds": "16406|16437|16486", "keyPointNames": "销售盈亏问题|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521444934492135424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "521444934492135424", "title": "重庆市育才中学教育集团2024−2025学年八年级上学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "521444943811878912", "questionArticle": "<p>2．已知关于 $ x,y $ 的二元一次方程组 $ \\begin{cases} x+2y=-3+2k \\\\ 2x+y=-1-k \\end{cases}  $ 的解满足 $ x+y\\leqslant  0 $ ，且关于 $ z $ 的不等式组 $ \\begin{cases} \\dfrac { z+3 } { 2 }-2 > k \\\\ z-1  <  4 \\end{cases}  $ 无解，那么所有符合条件的整数 $ k $ 的和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆市育才中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-10", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "521444934492135424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "521444934492135424", "title": "重庆市育才中学教育集团2024−2025学年八年级上学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "519614851615858688", "questionArticle": "<p>3．如图， $ \\vartriangle ABC $ 的周长为 $ {\\rm 24，} AC=8 $ ， $ BC $ 边上的中线 $ AE=5 $ ， $ \\vartriangle ABE $ 的周长为16，求 $ AB $ 的长．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/12/04/2/1/0/0/0/519614827536359441/images/img_18.png\" style=\"vertical-align:middle;\" width=\"148\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽合肥 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2024-12-08", "keyPointIds": "16439|30379", "keyPointNames": "几何问题|三角形的中线", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519614843453743104", "questionMethodName": "数形结合思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "519614843453743104", "title": "安徽省合肥寿春中学2024−2025学年八年级上学期 数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "517448130649956352", "questionArticle": "<p>4．某体育专卖店销售进价分别为100元，80元的 $ \\mathrm{ A } $ ， $ B $ 两种型号的乒乓球拍，下表是近两周的销售情况．（进价、售价均保持不变，利润=销售收入－进货成本）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量（块）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售收入（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i>型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i>型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>890</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1320</p></td></tr></table><p>(1)求<i>A</i>，<i>B</i>两种型号乒乓球拍的销售单价；</p><p>(2)若超市准备用不多于1850元的金额再采购这两种型号的乒乓球拍共20块，求 $ \\mathrm{ A } $ 型号乒乓球拍最多能采购多少块？</p><p>(3)在（2）的条件下（即超市用不多于1850元的金额采购这两种型号的乒乓球拍共20块），超市销售完这20块乒乓球拍能否实现利润超过500元的目标？若能，请给出相应的采购方案；若不能，请说明理由．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-07", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448122018078720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "517448122018078720", "title": "浙江省初中名校发展共同体期中联考2024−2025学年八年级上学期11月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "517448607949168640", "questionArticle": "<p>5．对<i>x</i>，<i>y</i>定义一种新运算<i>T</i>，规定： $ T\\left ( { x,y } \\right ) =axy+bx-4 $ （其中<i>a</i>，<i>b</i>均为非零常数），这里等式右边是通常的四则运算．例如： $ T\\left ( { 0,1 } \\right ) =a\\times 0\\times 1+b\\times 0-4=-4 $ ，若 $ T\\left ( { 3,1 } \\right ) =11 $ ， $ T\\left ( { -1,3 } \\right ) =-13 $ 则下列结论正确的有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>① $ a=2 $ ， $ b=3 $ ；</p><p>②若 $ T\\left ( { m,n } \\right ) =0\\left ( { n\\ne -\\dfrac { 3 } { 2 } } \\right )  $ ，则 $ m=\\dfrac { 4 } { 2n+3 } $ ；</p><p>③若 $ T\\left ( { m,n } \\right ) =0 $ ，则<i>m</i>，<i>n</i>有且仅有2组整数解；</p><p>④若无论 $ k $ 取何值时， $ T\\left ( { kx,y } \\right )  $ 的值均不变，则 $ y=-\\dfrac { 3 } { 2 } $ ；</p><p>⑤若 $ T\\left ( { kx,y } \\right ) =T\\left ( { ky,x } \\right )  $ 对任意有理数 $ x $ ， $ y $ 都成立，则 $ k=0 $ ．</p><p>A．2个B．3个C．4个D．5个</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆十一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448602077143040", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "517448602077143040", "title": "重庆十一中教育集团2024−2025学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "517448609933074432", "questionArticle": "<p>6．计算：</p><p>(1) $ \\begin{cases} y-2x=0 \\\\ 3x+y=15 \\end{cases}  $ ；</p><p>(2) $ -1{^{2023}}+\\left  | { \\sqrt { 3 }-2 } \\right  | -\\sqrt[3] { -27 }+\\sqrt { (-3){^{2}} } $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆十一中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-07", "keyPointIds": "16288|16290|16299|16424", "keyPointNames": "算术平方根|立方根|实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448602077143040", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "517448602077143040", "title": "重庆十一中教育集团2024−2025学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "517448607160639488", "questionArticle": "<p>7．在《九章算术》中，二元一次方程组是通过“算筹”摆放的，如图1、图2所示．图中各行从左到右列出的算筹数分别表示未知数 $ x $ ， $ y $ 的系数与相应的常数项．如图1所示的算筹图表示的方程组是 $ \\begin{cases} 2x+3y=27 \\\\ x+2y=14 \\end{cases}  $ ，类似的，图2所示的算筹图表示的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2024/11/28/2/1/0/0/0/517448518237200390/images/img_7.png\" style=\"vertical-align:middle;\" width=\"345\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 2x+y=11 \\\\ 4x+3y=22 \\end{cases}  $ B． $ \\begin{cases} 2x+y=11 \\\\ 4x+3y=27 \\end{cases}  $ </p><p>C． $ \\begin{cases} 2x+y=16 \\\\ 4x+3y=22 \\end{cases}  $ D． $ \\begin{cases} 2x+y=16 \\\\ 4x+3y=27 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆十一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448602077143040", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "517448602077143040", "title": "重庆十一中教育集团2024−2025学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "517448979371565056", "questionArticle": "<p>8．若关于 $ x $ 的一元一次不等式组 $ \\begin{cases} 2x-1  &lt;  3\\left ( { x-2 } \\right )  \\\\ \\dfrac { x-a } { 2 } &gt; 1 \\end{cases}  $ 的解集为 $ x &gt; 5 $ ，且关于<i>x</i><i>，</i><i>y</i>的二元一次方程组 $ \\begin{cases} x+5y=5 \\\\ x-3y=-4a-13 \\end{cases}  $ 的解满足 $ x+y  &lt;  0 $ ，则所有满足条件的整数 $ a $ 的值之和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2024-12-07", "keyPointIds": "16424|16485|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "517448971977007104", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "517448971977007104", "title": "重庆杨家坪中学2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "517448977186332672", "questionArticle": "<p>9．《九章算术》是中国传统数学的重要著作，方程术是它的最高成就．其中记载：“今有大器五、小器一容三斛；大器一、小器五容二斛．问大、小器各容几何？”译文：今有5个大容器和1个小容器可以装3斛（斛，音hú，是古代的一种容量单位），1个大容器、5个小容器可以装2斛．问：大容器、小容器分别可以装多少斛？设1个大容器装<i>x</i>斛，1个小容器装<i>y</i>斛，根据题意，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+y=3 \\\\ x+y=2 \\end{cases}  $ B． $ \\begin{cases} 5y+x=3 \\\\ x=y+2 \\end{cases}  $ C． $ \\begin{cases} 5x+y=3 \\\\ x+5y=2 \\end{cases}  $ D． $ \\begin{cases} 5x+y=2 \\\\ 5y+x=3 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2024-12-07", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "532701685816270848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "532701685816270848", "title": "湖南省怀化市五县六校联考2024−2025学年七年级上学期12月期末数学试题", "paperCategory": 1}, {"id": "517448971977007104", "title": "重庆杨家坪中学2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "519272791553646592", "questionArticle": "<p>10．我国古代数学名著《九章算术》记载了一道题，大意是几个人合买一件物品，每人出8元，剩余3元；每人出7元，还差4元.设有 $ x $ 人，该物品价值 $ y $ 元，根据题意，可列出的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A.  $ \\begin{cases}8x=y+3,\\\\ 7x=y-4\\end{cases} $ B.  $ \\begin{cases}8x=y+3,\\\\ 7x=y+4\\end{cases} $ </p><p>C.  $ \\begin{cases}8x=y-3,\\\\ 7x=y-4\\end{cases} $ D.  $ \\begin{cases}8x=y-3,\\\\ 7x=y+4\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|510000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川甘孜藏族自治州 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 3, "createTime": "2024-12-07", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "465823029316067328", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "465823029316067328", "title": "2024年四川省甘孜州中考数学试题", "paperCategory": 1}, {"id": "559116827223171072", "title": "湖北省潜江市初中联考协作体2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}, {"id": null, "title": "第6章 一次方程组全章综合训练刷中考《2025春初中必刷题 数学七年级下册 HS》", "paperCategory": 2}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 244, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 244, "timestamp": "2025-07-01T02:29:40.625Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}