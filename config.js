module.exports = {
    // ===== 必填配置 =====
    
    // Token 配置文件路径
    tokensFile: './tokens-4.json',  // tokens.json 文件路径
    paramsFile: './params.json',  // params.json 文件路径
    
    // Token 有效性检查配置
    enableTokenValidation: true,  // 是否启用token有效性检查
    tokenValidationInterval: 60, // token检查间隔(分钟)，0表示每次请求都检查
    
    // 浏览器配置
    headless: true,  // true=后台运行不显示浏览器，false=显示浏览器界面
    // 代理配置
    // proxyUrl: 'https://dps.kdlapi.com/api/getdps/?secret_id=oxx0d1rke44gdqgyn75o&signature=5pxc2ftyyxkwjqc4fpgglyd4d557c9wx&num=1&format=text&sep=1&f_auth=1&generateType=4', // 获取代理的URL，返回格式: user:pass@ip:port
    // proxyUrl: 'https://dps.kdlapi.com/api/getdps/?secret_id=o0f3a48rmeqt99gj3x2c&signature=3s7l49rqlutvw6trfpi7fosdsbc5xlrp&num=1&format=text&sep=1&f_auth=1&generateType=4',
    // proxyUrl: 'https://proxyapi.horocn.com/api/v2/proxies?order_id=BXXF1835049345194901&num=1&format=text&line_separator=win&can_repeat=no&user_token=854125eea02f0ba24257aec00cd10461',
    // ===== 可选配置 =====
    
    // 静态代理配置 (可选，优先级高于proxyUrl)
    // 如需使用静态代理，取消注释并配置
    proxy: {
        host: '127.0.0.1',
        port: 1080,
        protocol: 'http',  // 可选: http 或 https
        // auth: {            // 可选：代理认证
        //     username: '1714653636',
        //     password: 'triucylw'
        // }
    },
    
    // 并发配置
    concurrency: 1,  // 同时请求的页面数，建议1-5
    
    // 等待时间配置 (毫秒)
    minDelay: 2300,    // 最小等待时间
    maxDelay: 5500,   // 最大等待时间
    
    // ===== 缓存策略配置 =====
    enableCache: true,          // 启用浏览器缓存，减少重复请求，降低服务器负载
    cacheMaxSize: 100,         // 缓存大小限制 (MB)，建议50-200MB
    
    // ===== 防ban策略配置 =====
    enableAntiBot: true,        // 启用反反爬检测，包含浏览器指纹伪装等
    requestInterval: 1000,      // 请求基础间隔时间 (毫秒)，实际延迟会根据请求频率动态调整
    behaviorSimulation: true,   // 启用用户行为模拟，模拟真实用户操作
    scrollBehavior: true,       // 启用滚动行为模拟，随机进行页面滚动
    mouseBehavior: true,        // 启用鼠标行为模拟，随机鼠标移动和点击
    
    // 分页配置
    startPage: 1,    // 起始页面
    pageSize: 10,    // 每页数据量 (建议不要修改)
    // maxPages: 100, // 最大页面数，null为无限制
    
    // ===== 请求参数配置 =====
    // 注意：以下参数中，只有 studyPhaseCode、subjectCode、textbookVersionCode、ceciCode 
    // 会根据 params.json 动态覆盖，其他参数保持固定值
    params: {
        "studyPhaseCode": "300",     // 学习阶段代码
        "subjectCode": "2",          // 学科代码
        "textbookVersionCode": "30", // 教材版本代码
        "ceciCode": "143",           // CECI代码
        "searchType": 1,             // 搜索类型
        "sort": 0,                   // 排序方式
        "yearCode": "",              // 年份代码
        "gradeCode": "",             // 年级代码
        "provinceCode": "",          // 省份代码
        "cityCode": "",              // 城市代码
        "areaCode": "",              // 地区代码
        "organizationCode": "",      // 组织代码
        "termCode": "",              // 学期代码
        "keyWord": "",               // 关键词
        "filterQuestionFlag": false, // 过滤问题标识
        "searchScope": 0,            // 搜索范围
        "treeIds": [],               // 树形ID数组
        "categoryId": ""             // 分类ID
    },

    // ===== 飞书通知配置 =====
    feishu: {
        enabled: true,  // 是否启用飞书通知
        webhookUrl: 'https://open.feishu.cn/open-apis/bot/v2/hook/f020d858-9138-4c24-ac35-32faca00a0d5',
        secret: 'PUCczmZWR1u9V5w1qlTlZb',  // 签名校验密钥
        timeout: 10000  // 请求超时时间(毫秒)
    }
};