const crypto = require('crypto');
const https = require('https');

/**
 * 飞书机器人通知工具类
 * 用于在程序退出或重要事件时发送通知
 */
class FeishuNotifier {
    constructor(config = {}) {
        this.webhookUrl = config.webhookUrl || 'https://open.feishu.cn/open-apis/bot/v2/hook/f020d858-9138-4c24-ac35-32faca00a0d5';
        this.secret = config.secret || 'PUCczmZWR1u9V5w1qlTlZb';
        this.enabled = config.enabled !== false; // 默认启用
        this.timeout = config.timeout || 10000; // 10秒超时
        this.logger = config.logger;
    }

    /**
     * 生成签名
     * @param {number} timestamp - 时间戳
     * @param {string} secret - 密钥
     * @returns {string} 签名
     */
    generateSign(timestamp, secret) {
        // 拼接timestamp和secret
        const stringToSign = `${timestamp}\n${secret}`;
        const hmacCode = crypto.createHmac('sha256', stringToSign).digest();
        
        // 对结果进行base64处理
        const sign = hmacCode.toString('base64');
        
        return sign;
    }

    /**
     * 发送飞书通知
     * @param {Object} options - 通知选项
     * @param {string} options.title - 通知标题
     * @param {string} options.content - 通知内容
     * @param {string} options.msgType - 消息类型，默认为 'text'
     * @param {Object} options.extra - 额外信息
     * @returns {Promise<boolean>} 发送是否成功
     */
    async sendNotification(options = {}) {
        if (!this.enabled) {
            this.logger?.debug('飞书通知已禁用，跳过发送');
            return false;
        }

        try {
            const {
                title = '系统通知',
                content = '',
                msgType = 'text',
                extra = {}
            } = options;

            // 生成时间戳和签名
            const timestamp = Math.floor(Date.now() / 1000);
            const sign = this.generateSign(timestamp, this.secret);

            // 构建消息内容
            let messageContent;
            if (msgType === 'text') {
                messageContent = this.buildTextMessage(title, content, extra);
            } else if (msgType === 'rich_text') {
                messageContent = this.buildRichTextMessage(title, content, extra);
            } else {
                messageContent = content;
            }

            // 构建请求体
            const payload = {
                timestamp: timestamp.toString(),
                sign: sign,
                msg_type: msgType,
                content: {
                    text: messageContent
                }
            };

            // 发送请求
            const success = await this.sendRequest(payload);
            
            if (success) {
                this.logger?.info('✅ 飞书通知发送成功', { title, timestamp });
            } else {
                this.logger?.error('❌ 飞书通知发送失败', { title, timestamp });
            }

            return success;

        } catch (error) {
            this.logger?.error('❌ 发送飞书通知时出错', { 
                error: error.message,
                title: options.title 
            });
            return false;
        }
    }

    /**
     * 构建文本消息
     */
    buildTextMessage(title, content, extra) {
        const timestamp = new Date().toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai'
        });

        let message = `📢 ${title}\n\n`;
        
        if (content) {
            message += `${content}\n\n`;
        }

        // 添加额外信息
        if (extra && Object.keys(extra).length > 0) {
            message += '📊 详细信息:\n';
            for (const [key, value] of Object.entries(extra)) {
                message += `• ${key}: ${value}\n`;
            }
            message += '\n';
        }

        message += `🕐 时间: ${timestamp}`;

        return message;
    }

    /**
     * 构建富文本消息
     */
    buildRichTextMessage(title, content, extra) {
        // 简化版富文本，实际可以根据需要扩展
        return this.buildTextMessage(title, content, extra);
    }

    /**
     * 发送HTTP请求
     */
    async sendRequest(payload) {
        return new Promise((resolve) => {
            const data = JSON.stringify(payload);
            
            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(data)
                },
                timeout: this.timeout
            };

            const req = https.request(this.webhookUrl, options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(responseData);
                        const success = response.code === 0 || response.StatusCode === 0;
                        
                        if (!success) {
                            this.logger?.error('飞书API返回错误', { 
                                response: responseData,
                                statusCode: res.statusCode 
                            });
                        }
                        
                        resolve(success);
                    } catch (parseError) {
                        this.logger?.error('解析飞书响应失败', { 
                            error: parseError.message,
                            response: responseData 
                        });
                        resolve(false);
                    }
                });
            });

            req.on('error', (error) => {
                this.logger?.error('飞书请求失败', { error: error.message });
                resolve(false);
            });

            req.on('timeout', () => {
                this.logger?.error('飞书请求超时');
                req.destroy();
                resolve(false);
            });

            req.write(data);
            req.end();
        });
    }

    /**
     * 发送Token耗尽通知
     */
    async sendTokenExhaustedNotification(details = {}) {
        const {
            availableTokens = 0,
            totalTokens = 0,
            currentParam = 'unknown',
            instanceId = 'unknown',
            duration = 'unknown',
            tokenFile = 'unknown'
        } = details;

        return await this.sendNotification({
            title: '🚫 Token已耗尽 - 程序退出',
            content: '爬虫程序因所有Token已耗尽而停止运行',
            extra: {
                '可用Token数': availableTokens,
                '总Token数': totalTokens,
                '当前处理参数': currentParam,
                '实例ID': instanceId,
                '运行时长': duration,
                'Token文件': tokenFile,
                '退出原因': 'Token资源耗尽',
                '建议操作': '请检查Token状态或等待Token恢复后重新启动程序'
            }
        });
    }

    /**
     * 发送IP封禁通知
     */
    async sendIpBannedNotification(details = {}) {
        const {
            instanceId = 'unknown',
            tokenFile = 'unknown',
            currentParam = 'unknown',
            duration = 'unknown',
            ipAddress = 'unknown'
        } = details;

        return await this.sendNotification({
            title: '🚫 IP地址被封禁 - 程序退出',
            content: '爬虫程序因IP地址被封禁而停止运行',
            extra: {
                '封禁类型': 'IP地址封禁',
                '当前处理参数': currentParam,
                '实例ID': instanceId,
                '运行时长': duration,
                'Token文件': tokenFile,
                'IP地址': ipAddress,
                '退出原因': 'IP地址被目标服务器封禁',
                '建议操作': '请更换IP地址或等待封禁解除后重新启动程序'
            }
        });
    }

    /**
     * 发送系统错误通知
     */
    async sendSystemErrorNotification(error, context = {}) {
        const {
            type = 'unknown',
            instanceId = 'unknown',
            tokenFile = 'unknown',
            ...otherContext
        } = context;

        return await this.sendNotification({
            title: '💥 系统错误',
            content: `程序遇到严重错误: ${error.message}`,
            extra: {
                '错误类型': error.name || 'Unknown',
                '错误信息': error.message,
                '错误分类': type,
                '实例ID': instanceId,
                'Token文件': tokenFile,
                '其他信息': Object.keys(otherContext).length > 0 ? JSON.stringify(otherContext) : '无',
                '时间戳': new Date().toISOString()
            }
        });
    }

    /**
     * 发送程序启动通知
     */
    async sendStartupNotification(config = {}) {
        return await this.sendNotification({
            title: '🚀 爬虫程序启动',
            content: '爬虫程序已成功启动并开始运行',
            extra: {
                '实例ID': config.instanceId || 'unknown',
                '最大并发数': config.maxConcurrency || 'unknown',
                '配置模式': config.mode || 'unknown',
                '启动时间': new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
            }
        });
    }

    /**
     * 发送程序完成通知
     */
    async sendCompletionNotification(stats = {}) {
        return await this.sendNotification({
            title: '✅ 爬虫程序完成',
            content: '爬虫程序已成功完成所有任务',
            extra: {
                '处理参数数': stats.processedParams || 0,
                '成功数': stats.successCount || 0,
                '失败数': stats.failureCount || 0,
                '总运行时长': stats.totalDuration || 'unknown',
                '完成时间': new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
            }
        });
    }
}

module.exports = FeishuNotifier;
