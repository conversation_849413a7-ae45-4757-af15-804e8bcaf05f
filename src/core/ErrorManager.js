/**
 * Token Exhausted Exception
 * Special exception for when all tokens are exhausted or invalid
 */
class TokenExhaustedException extends Error {
    constructor(message = '所有可用令牌已耗尽或无效', availableTokens = 0, totalTokens = 0) {
        super(message);
        this.name = 'TokenExhaustedException';
        this.category = 'TOKEN_EXHAUSTED';
        this.availableTokens = availableTokens;
        this.totalTokens = totalTokens;
        this.timestamp = new Date().toISOString();
        this.retryable = false;
        this.severity = 'CRITICAL';
    }
}

/**
 * Comprehensive Error Management System
 * Provides error categorization, recovery strategies, and circuit breaker functionality
 */
class ErrorManager {
    constructor(config = {}) {
        this.config = {
            maxRetryAttempts: config.maxRetryAttempts || 3,
            retryBackoffMultiplier: config.retryBackoffMultiplier || 2,
            circuitBreakerThreshold: config.circuitBreakerThreshold || 5,
            circuitBreakerTimeout: config.circuitBreakerTimeout || 300000, // 5 minutes
            errorReportingEnabled: config.errorReportingEnabled !== false,
            ...config
        };

        // Error tracking
        this.errorCounts = new Map();
        this.circuitBreakers = new Map();
        this.errorHistory = [];
        this.recoveryStrategies = new Map();
        
        this.setupErrorCategories();
        this.setupRecoveryStrategies();
    }

    /**
     * Error categories with detailed classification
     */
    static ERROR_CATEGORIES = {
        NETWORK: {
            code: 'NETWORK',
            description: 'Network connectivity issues',
            severity: 'HIGH',
            retryable: true
        },
        AUTHENTICATION: {
            code: 'AUTHENTICATION',
            description: 'Authentication or authorization failures',
            severity: 'HIGH',
            retryable: false
        },
        RATE_LIMIT: {
            code: 'RATE_LIMIT',
            description: 'Rate limiting or throttling',
            severity: 'MEDIUM',
            retryable: true
        },
        TOKEN_EXPIRED: {
            code: 'TOKEN_EXPIRED',
            description: 'Token has expired',
            severity: 'MEDIUM',
            retryable: false
        },
        TOKEN_INVALID: {
            code: 'TOKEN_INVALID',
            description: 'Token is invalid or malformed',
            severity: 'HIGH',
            retryable: false
        },
        TOKEN_EXHAUSTED: {
            code: 'TOKEN_EXHAUSTED',
            description: 'All available tokens have been exhausted or are invalid',
            severity: 'CRITICAL',
            retryable: false
        },
        BROWSER: {
            code: 'BROWSER',
            description: 'Browser or page-related errors',
            severity: 'MEDIUM',
            retryable: true
        },
        PARSING: {
            code: 'PARSING',
            description: 'Data parsing or extraction errors',
            severity: 'LOW',
            retryable: true
        },
        TIMEOUT: {
            code: 'TIMEOUT',
            description: 'Request or operation timeout',
            severity: 'MEDIUM',
            retryable: true
        },
        SERVER_ERROR: {
            code: 'SERVER_ERROR',
            description: 'Server-side errors (5xx)',
            severity: 'HIGH',
            retryable: true
        },
        CLIENT_ERROR: {
            code: 'CLIENT_ERROR',
            description: 'Client-side errors (4xx)',
            severity: 'MEDIUM',
            retryable: false
        },
        PROXY: {
            code: 'PROXY',
            description: 'Proxy-related errors',
            severity: 'MEDIUM',
            retryable: true
        },
        RESOURCE: {
            code: 'RESOURCE',
            description: 'Resource exhaustion (memory, disk, etc.)',
            severity: 'HIGH',
            retryable: false
        },
        UNKNOWN: {
            code: 'UNKNOWN',
            description: 'Unclassified errors',
            severity: 'MEDIUM',
            retryable: true
        }
    };

    /**
     * Setup error categories and patterns
     */
    setupErrorCategories() {
        this.errorPatterns = new Map([
            // Network errors
            [/ECONNREFUSED|ENOTFOUND|ECONNRESET|ETIMEDOUT|EHOSTUNREACH/i, 'NETWORK'],
            [/net::ERR_|NetworkError|fetch.*failed/i, 'NETWORK'],
            
            // Authentication errors
            [/401|unauthorized|authentication.*failed|invalid.*credentials/i, 'AUTHENTICATION'],
            [/403|forbidden|access.*denied/i, 'AUTHENTICATION'],
            
            // Rate limiting
            [/429|too.*many.*requests|rate.*limit|throttle/i, 'RATE_LIMIT'],
            
            // Token errors
            [/token.*expired|jwt.*expired|expired.*token/i, 'TOKEN_EXPIRED'],
            [/invalid.*token|malformed.*token|token.*invalid/i, 'TOKEN_INVALID'],
            [/没有可用令牌|没有可用.*token|token.*exhausted|all.*tokens.*invalid/i, 'TOKEN_EXHAUSTED'],
            
            // Browser errors
            [/page.*crashed|browser.*disconnected|target.*closed/i, 'BROWSER'],
            [/navigation.*failed|page.*not.*found/i, 'BROWSER'],
            
            // Timeout errors
            [/timeout|timed.*out|deadline.*exceeded/i, 'TIMEOUT'],
            
            // Server errors
            [/5\d{2}|internal.*server.*error|service.*unavailable/i, 'SERVER_ERROR'],
            
            // Client errors
            [/4\d{2}|bad.*request|not.*found/i, 'CLIENT_ERROR'],
            
            // Proxy errors
            [/proxy.*error|proxy.*failed|tunnel.*failed/i, 'PROXY'],
            
            // Resource errors
            [/out.*of.*memory|disk.*full|resource.*exhausted/i, 'RESOURCE']
        ]);
    }

    /**
     * Setup recovery strategies for each error category
     */
    setupRecoveryStrategies() {
        this.recoveryStrategies.set('NETWORK', {
            retryable: true,
            maxRetries: 3,
            backoffMultiplier: 2,
            actions: ['wait', 'retry', 'switch_proxy']
        });

        this.recoveryStrategies.set('AUTHENTICATION', {
            retryable: false,
            maxRetries: 0,
            actions: ['invalidate_token', 'get_new_token']
        });

        this.recoveryStrategies.set('RATE_LIMIT', {
            retryable: true,
            maxRetries: 5,
            backoffMultiplier: 3,
            actions: ['wait_longer', 'switch_token', 'reduce_concurrency']
        });

        this.recoveryStrategies.set('TOKEN_EXPIRED', {
            retryable: false,
            maxRetries: 0,
            actions: ['refresh_token', 'get_new_token']
        });

        this.recoveryStrategies.set('TOKEN_INVALID', {
            retryable: false,
            maxRetries: 0,
            actions: ['invalidate_token', 'get_new_token']
        });

        this.recoveryStrategies.set('TOKEN_EXHAUSTED', {
            retryable: false,
            maxRetries: 0,
            actions: ['exit_program', 'notify_admin']
        });

        this.recoveryStrategies.set('BROWSER', {
            retryable: true,
            maxRetries: 2,
            backoffMultiplier: 1.5,
            actions: ['restart_browser', 'clear_cache', 'retry']
        });

        this.recoveryStrategies.set('PARSING', {
            retryable: true,
            maxRetries: 2,
            backoffMultiplier: 1,
            actions: ['retry', 'fallback_parser']
        });

        this.recoveryStrategies.set('TIMEOUT', {
            retryable: true,
            maxRetries: 3,
            backoffMultiplier: 2,
            actions: ['increase_timeout', 'retry']
        });

        this.recoveryStrategies.set('SERVER_ERROR', {
            retryable: true,
            maxRetries: 3,
            backoffMultiplier: 2,
            actions: ['wait', 'retry', 'switch_endpoint']
        });

        this.recoveryStrategies.set('PROXY', {
            retryable: true,
            maxRetries: 2,
            backoffMultiplier: 1,
            actions: ['switch_proxy', 'retry']
        });

        this.recoveryStrategies.set('RESOURCE', {
            retryable: false,
            maxRetries: 0,
            actions: ['cleanup_resources', 'reduce_concurrency', 'alert']
        });
    }

    /**
     * Categorize error based on message and context
     */
    categorizeError(error, context = {}) {
        const errorMessage = error.message || error.toString();
        const errorCode = error.code || error.status || error.statusCode;
        
        // Check HTTP status codes first
        if (errorCode) {
            if (errorCode === 401 || errorCode === 403) return 'AUTHENTICATION';
            if (errorCode === 429) return 'RATE_LIMIT';
            if (errorCode >= 500) return 'SERVER_ERROR';
            if (errorCode >= 400) return 'CLIENT_ERROR';
        }

        // Check error message patterns
        for (const [pattern, category] of this.errorPatterns) {
            if (pattern.test(errorMessage)) {
                return category;
            }
        }

        // Check context-specific categorization
        if (context.operation === 'token_validation' && errorMessage.includes('expired')) {
            return 'TOKEN_EXPIRED';
        }

        if (context.operation === 'browser_navigation' && errorMessage.includes('timeout')) {
            return 'TIMEOUT';
        }

        return 'UNKNOWN';
    }

    /**
     * Create enhanced error object with categorization and metadata
     */
    createEnhancedError(error, context = {}) {
        const category = this.categorizeError(error, context);
        const categoryInfo = ErrorManager.ERROR_CATEGORIES[category];
        
        const enhancedError = {
            original: error,
            category: category,
            categoryInfo: categoryInfo,
            message: error.message || error.toString(),
            code: error.code || error.status || error.statusCode,
            timestamp: new Date().toISOString(),
            context: context,
            retryable: categoryInfo.retryable,
            severity: categoryInfo.severity,
            recoveryStrategy: this.recoveryStrategies.get(category)
        };

        // Track error occurrence
        this.trackError(enhancedError);

        return enhancedError;
    }

    /**
     * Track error occurrence for analytics and circuit breaker
     */
    trackError(enhancedError) {
        const category = enhancedError.category;
        
        // Update error counts
        if (!this.errorCounts.has(category)) {
            this.errorCounts.set(category, 0);
        }
        this.errorCounts.set(category, this.errorCounts.get(category) + 1);

        // Add to error history
        this.errorHistory.push({
            category: category,
            timestamp: enhancedError.timestamp,
            message: enhancedError.message,
            context: enhancedError.context
        });

        // Keep only recent errors (last 1000)
        if (this.errorHistory.length > 1000) {
            this.errorHistory = this.errorHistory.slice(-1000);
        }

        // Check circuit breaker
        this.checkCircuitBreaker(category);
    }

    /**
     * Check and update circuit breaker status
     */
    checkCircuitBreaker(category) {
        const errorCount = this.errorCounts.get(category) || 0;
        
        if (errorCount >= this.config.circuitBreakerThreshold) {
            if (!this.circuitBreakers.has(category)) {
                console.warn(`🔴 Circuit breaker OPEN for category: ${category}`);
                this.circuitBreakers.set(category, {
                    state: 'OPEN',
                    openedAt: Date.now(),
                    errorCount: errorCount
                });

                // Set timeout to half-open circuit breaker
                setTimeout(() => {
                    if (this.circuitBreakers.has(category)) {
                        const breaker = this.circuitBreakers.get(category);
                        breaker.state = 'HALF_OPEN';
                        console.warn(`🟡 Circuit breaker HALF-OPEN for category: ${category}`);
                    }
                }, this.config.circuitBreakerTimeout);
            }
        }
    }

    /**
     * Check if operation should be allowed based on circuit breaker
     */
    isOperationAllowed(category) {
        const breaker = this.circuitBreakers.get(category);
        if (!breaker) return true;

        if (breaker.state === 'OPEN') {
            return false;
        }

        if (breaker.state === 'HALF_OPEN') {
            // Allow limited operations to test if service recovered
            return Math.random() < 0.1; // 10% chance
        }

        return true;
    }

    /**
     * Reset circuit breaker for a category
     */
    resetCircuitBreaker(category) {
        if (this.circuitBreakers.has(category)) {
            this.circuitBreakers.delete(category);
            this.errorCounts.set(category, 0);
            console.log(`✅ Circuit breaker CLOSED for category: ${category}`);
        }
    }

    /**
     * Get recovery strategy for error
     */
    getRecoveryStrategy(enhancedError) {
        return enhancedError.recoveryStrategy || this.recoveryStrategies.get('UNKNOWN');
    }

    /**
     * Calculate retry delay with exponential backoff
     */
    calculateRetryDelay(attempt, category = 'UNKNOWN') {
        const strategy = this.recoveryStrategies.get(category);
        const multiplier = strategy?.backoffMultiplier || this.config.retryBackoffMultiplier;
        const baseDelay = 1000; // 1 second base delay
        
        return Math.min(baseDelay * Math.pow(multiplier, attempt), 30000); // Max 30 seconds
    }

    /**
     * Get error statistics
     */
    getErrorStatistics() {
        const now = Date.now();
        const oneHourAgo = now - 3600000; // 1 hour ago
        
        const recentErrors = this.errorHistory.filter(
            error => new Date(error.timestamp).getTime() > oneHourAgo
        );

        const categoryCounts = {};
        for (const [category, count] of this.errorCounts) {
            categoryCounts[category] = count;
        }

        const circuitBreakerStatus = {};
        for (const [category, breaker] of this.circuitBreakers) {
            circuitBreakerStatus[category] = breaker.state;
        }

        return {
            totalErrors: this.errorHistory.length,
            recentErrors: recentErrors.length,
            categoryCounts: categoryCounts,
            circuitBreakerStatus: circuitBreakerStatus,
            errorRate: recentErrors.length / 60 // errors per minute
        };
    }

    /**
     * Clear error history and reset counters
     */
    reset() {
        this.errorCounts.clear();
        this.circuitBreakers.clear();
        this.errorHistory = [];
        console.log('✅ Error manager reset completed');
    }
}

module.exports = ErrorManager;
module.exports.TokenExhaustedException = TokenExhaustedException;
